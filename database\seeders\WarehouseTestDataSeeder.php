<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Product;
use App\Models\Order;
use App\Models\Cart;
use App\Models\Color;
use App\Models\ItemColor;
use Illuminate\Support\Facades\Hash;

class WarehouseTestDataSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create test picker user
        $picker = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'John',
                'last_name' => 'Picker',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'picker',
                'status' => 'active',
                'contact_phone' => '555-0101',
                'account_phone' => '555-0101'
            ]
        );

        // Create test customer
        $customer = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Jane',
                'last_name' => 'Customer',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'customer',
                'status' => 'active',
                'contact_phone' => '555-0102',
                'account_phone' => '555-0102'
            ]
        );

        // Create test salesman
        $salesman = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Bob',
                'last_name' => 'Salesman',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'salesman',
                'status' => 'active',
                'contact_phone' => '555-0103',
                'account_phone' => '555-0103'
            ]
        );

        // Create test colors
        $colors = [
            ['name' => 'Red', 'color' => '#FF0000'],
            ['name' => 'Blue', 'color' => '#0000FF'],
            ['name' => 'Green', 'color' => '#00FF00'],
            ['name' => 'Black', 'color' => '#000000'],
            ['name' => 'White', 'color' => '#FFFFFF'],
        ];

        foreach ($colors as $colorData) {
            Color::firstOrCreate(['name' => $colorData['name']], $colorData);
        }

        // Create test products using only existing fields
        $products = [
            [
                'title' => 'Premium Office Chair',
                'slug' => 'premium-office-chair',
                'summary' => 'Ergonomic office chair with lumbar support',
                'description' => 'High-quality office chair designed for comfort and productivity.',
                'stock' => 25,
                'status' => 'active',
                'cat_id' => 1,
                'price' => 299.99,
                'discount' => 10,
                'weight' => 35.5
            ],
            [
                'title' => 'Wireless Bluetooth Headphones',
                'slug' => 'wireless-bluetooth-headphones',
                'summary' => 'High-quality wireless headphones with noise cancellation',
                'description' => 'Premium wireless headphones with superior sound quality.',
                'stock' => 50,
                'status' => 'active',
                'cat_id' => 1,
                'price' => 149.99,
                'discount' => 15,
                'weight' => 0.8
            ],
            [
                'title' => 'Stainless Steel Water Bottle',
                'slug' => 'stainless-steel-water-bottle',
                'summary' => 'Insulated water bottle keeps drinks cold for 24 hours',
                'description' => 'Durable stainless steel water bottle with vacuum insulation.',
                'stock' => 15, // Low stock to test backorders
                'status' => 'active',
                'cat_id' => 1,
                'price' => 29.99,
                'discount' => 0,
                'weight' => 1.2
            ],
            [
                'title' => 'Laptop Stand Adjustable',
                'slug' => 'laptop-stand-adjustable',
                'summary' => 'Ergonomic laptop stand with adjustable height',
                'description' => 'Aluminum laptop stand that improves posture and airflow.',
                'stock' => 5, // Very low stock to test backorders
                'status' => 'active',
                'cat_id' => 1,
                'price' => 79.99,
                'discount' => 5,
                'weight' => 2.5
            ]
        ];

        foreach ($products as $productData) {
            $product = Product::firstOrCreate(
                ['slug' => $productData['slug']],
                $productData
            );

            // Add color variations for each product
            $productColors = Color::take(3)->get();
            foreach ($productColors as $color) {
                ItemColor::firstOrCreate([
                    'product_id' => $product->id,
                    'color_id' => $color->id
                ], [
                    'stock' => rand(5, 20)
                ]);
            }
        }

        // Create test orders
        $this->createTestOrders($customer, $salesman, $picker);

        $this->command->info('Warehouse test data seeded successfully!');
        $this->command->info('Test Users Created:');
        $this->command->info('- Picker: <EMAIL> / password');
        $this->command->info('- Customer: <EMAIL> / password');
        $this->command->info('- Salesman: <EMAIL> / password');
    }

    private function createTestOrders($customer, $salesman, $picker)
    {
        $products = Product::all();
        $colors = Color::all();

        // Create order ready for assignment
        $order1 = Order::create([
            'order_number' => 'ORD-' . time() . '-001',
            'user_id' => $customer->id,
            'salesman_id' => $salesman->id,
            'sub_total' => 379.98,
            'total_amount' => 379.98,
            'quantity' => 3,
            'first_name' => $customer->first_name,
            'last_name' => $customer->last_name,
            'email' => $customer->email,
            'phone' => $customer->contact_phone,
            'country' => 'USA',
            'post_code' => '12345',
            'address1' => '123 Test Street',
            'address2' => 'Apt 4B',
            'delivery_method' => 'standard',
            'delivery_instructions' => 'Leave at front door if no answer',
            'status' => 'pending'
        ]);

        // Add items to order 1
        $this->addOrderItems($order1, $products, $colors);

        // Create order assigned to picker
        $order2 = Order::create([
            'order_number' => 'ORD-' . time() . '-002',
            'user_id' => $customer->id,
            'salesman_id' => $salesman->id,
            'picker_id' => $picker->id,
            'sub_total' => 229.98,
            'total_amount' => 229.98,
            'quantity' => 2,
            'first_name' => $customer->first_name,
            'last_name' => $customer->last_name,
            'email' => $customer->email,
            'phone' => $customer->contact_phone,
            'country' => 'USA',
            'post_code' => '12345',
            'address1' => '123 Test Street',
            'address2' => 'Apt 4B',
            'delivery_method' => 'express',
            'delivery_instructions' => 'Ring doorbell twice',
            'status' => 'sent_to_warehouse'
        ]);

        // Add items to order 2
        $this->addOrderItems($order2, $products->take(2), $colors);

        // Create order being processed
        $order3 = Order::create([
            'order_number' => 'ORD-' . time() . '-003',
            'user_id' => $customer->id,
            'salesman_id' => $salesman->id,
            'picker_id' => $picker->id,
            'sub_total' => 159.98,
            'total_amount' => 159.98,
            'quantity' => 4,
            'first_name' => $customer->first_name,
            'last_name' => $customer->last_name,
            'email' => $customer->email,
            'phone' => $customer->contact_phone,
            'country' => 'USA',
            'post_code' => '12345',
            'address1' => '123 Test Street',
            'address2' => 'Apt 4B',
            'delivery_method' => 'pickup',
            'status' => 'processing'
        ]);

        // Add items to order 3 (including low stock items for backorder testing)
        $this->addOrderItemsWithBackorders($order3, $products, $colors);
    }

    private function addOrderItems($order, $products, $colors)
    {
        foreach ($products->take(2) as $index => $product) {
            $color = $colors->random();
            $quantity = rand(1, 3);

            Cart::create([
                'user_id' => $order->user_id,
                'product_id' => $product->id,
                'order_id' => $order->id,
                'quantity' => $quantity,
                'price' => $product->price,
                'amount' => $product->price * $quantity,
                'color' => $color->id,
                'status' => 'new'
            ]);
        }
    }

    private function addOrderItemsWithBackorders($order, $products, $colors)
    {
        // Add items that will cause backorders (high quantities of low-stock items)
        $lowStockProduct = $products->where('slug', 'laptop-stand-adjustable')->first(); // Laptop stand with 5 stock
        $color = $colors->random();

        Cart::create([
            'user_id' => $order->user_id,
            'product_id' => $lowStockProduct->id,
            'order_id' => $order->id,
            'quantity' => 8, // More than available stock
            'price' => $lowStockProduct->price,
            'amount' => $lowStockProduct->price * 8,
            'color' => $color->id,
            'status' => 'new'
        ]);

        // Add normal item
        $normalProduct = $products->where('slug', 'premium-office-chair')->first();
        Cart::create([
            'user_id' => $order->user_id,
            'product_id' => $normalProduct->id,
            'order_id' => $order->id,
            'quantity' => 2,
            'price' => $normalProduct->price,
            'amount' => $normalProduct->price * 2,
            'color' => $colors->random()->id,
            'status' => 'new'
        ]);
    }
}
