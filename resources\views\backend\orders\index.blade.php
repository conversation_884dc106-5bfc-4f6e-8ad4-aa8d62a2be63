@extends('backend.layouts.master')

@section('main-content')
 <!-- DataTales Example -->
 <div class="card shadow mb-4">
     <div class="row">
         <div class="col-md-12">
            @include('backend.layouts.notification')
         </div>
     </div>
    <div class="card-header py-3">
      <h6 class="m-0 font-weight-bold text-primary float-left">Order Lists</h6>
      <div class="float-right">
        <button class="btn btn-success btn-sm mr-2" id="bulkAssignBtn" style="display: none;">
          <i class="fas fa-user-plus"></i> Assign to Picker
        </button>
        <a href="{{route('orders.create')}}" class="btn btn-primary btn-sm" data-toggle="tooltip" data-placement="bottom" title="Add Order">
          <i class="fas fa-plus"></i> Add Order
        </a>
      </div>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        @if(count($orders)>0)
        <table class="table table-bordered" id="order-dataTable" width="100%" cellspacing="0">
          <thead>
            <tr>
              <th><input type="checkbox" id="selectAll"></th>
              <th>Order No.</th>
              <th>Customer</th>
              <th>Items</th>
              <th>Total Amount</th>
              <th>Status</th>
              <th>Picker</th>
              <th>Action</th>
            </tr>
          </thead>
          <tfoot>
            <tr>
              <th><input type="checkbox" disabled></th>
              <th>Order No.</th>
              <th>Customer</th>
              <th>Items</th>
              <th>Total Amount</th>
              <th>Status</th>
              <th>Picker</th>
              <th>Action</th>
            </tr>
          </tfoot>
          <tbody>
            @foreach($orders as $order)
                <tr>
                    <td>
                      <input type="checkbox" class="order-checkbox" value="{{$order->id}}"
                             {{$order->picker_id ? 'disabled' : ''}}>
                    </td>
                    <td><strong>{{$order->order_number}}</strong></td>
                    <td>{{$order->first_name}} {{$order->last_name}}</td>
                    <td>
                      <span class="badge badge-info">{{$order->cart_info->count()}} items</span>
                    </td>
                    <td>${{number_format($order->total_amount,2)}}</td>
                    <td>
                        @if($order->status=='pending')
                          <span class="badge badge-primary">Pending</span>
                        @elseif($order->status=='sent_to_warehouse')
                          <span class="badge badge-warning">Sent to Warehouse</span>
                        @elseif($order->status=='processing')
                          <span class="badge badge-info">Being Packed</span>
                        @elseif($order->status=='shipped')
                          <span class="badge badge-success">Shipped</span>
                        @elseif($order->status=='delivered')
                          <span class="badge badge-success">Delivered</span>
                        @else
                          <span class="badge badge-secondary">{{ucfirst($order->status)}}</span>
                        @endif
                    </td>
                    <td>
                      @if($order->picker_id)
                        @php
                          $picker = \App\Models\User::find($order->picker_id);
                        @endphp
                        <span class="badge badge-success">
                          {{$picker ? $picker->first_name . ' ' . $picker->last_name : 'Unknown'}}
                        </span>
                      @else
                        <button class="btn btn-sm btn-outline-primary assign-picker-btn"
                                data-order-id="{{$order->id}}" data-order-number="{{$order->order_number}}">
                          <i class="fas fa-user-plus"></i> Assign
                        </button>
                      @endif
                    </td>
                    <td>
                        <a href="{{route('orders.show',$order->id)}}" class="btn btn-warning btn-sm mr-1" style="height:30px; width:30px;border-radius:50%" data-toggle="tooltip" title="view" data-placement="bottom"><i class="fas fa-eye"></i></a>
                        <a href="{{ route('orders.edit', $order->id) }}" class="btn btn-primary btn-sm mr-1" style="height:30px; width:30px;border-radius:50%" data-toggle="tooltip" title="edit" data-placement="bottom"><i class="fas fa-edit"></i></a>
                        <form method="POST" action="{{ route('orders.destroy', $order->id) }}" style="display: inline;">
                          @csrf
                          @method('delete')
                          <button class="btn btn-danger btn-sm dltBtn" data-id={{$order->id}} style="height:30px; width:30px;border-radius:50%" data-toggle="tooltip" data-placement="bottom" title="Delete"><i class="fas fa-trash-alt"></i></button>
                        </form>
                    </td>
                </tr>
            @endforeach
          </tbody>
        </table>
        <span style="float:right">{{$orders->links()}}</span>
        @else
          <h6 class="text-center">No orders found!!! Please order some products</h6>
        @endif
      </div>
    </div>
</div>

<!-- Single Order Assignment Modal -->
<div class="modal fade" id="assignPickerModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Assign Order to Picker</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="assignPickerForm">
                    <input type="hidden" id="assignOrderId" name="order_id">
                    <div class="form-group">
                        <label>Order Number:</label>
                        <span id="assignOrderNumber" class="font-weight-bold"></span>
                    </div>
                    <div class="form-group">
                        <label for="assignPickerId">Select Picker:</label>
                        <select class="form-control" id="assignPickerId" name="picker_id" required>
                            <option value="">-- Select Picker --</option>
                            @foreach(\App\Models\User::where('role', 'picker')->where('status', 'active')->get() as $picker)
                                <option value="{{$picker->id}}">{{$picker->first_name}} {{$picker->last_name}}</option>
                            @endforeach
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmAssignPicker">Assign Order</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Assignment Modal -->
<div class="modal fade" id="bulkAssignModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Assign Orders to Picker</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="bulkAssignForm">
                    <div class="form-group">
                        <label>Selected Orders:</label>
                        <div id="selectedOrdersList" class="border p-2 bg-light" style="max-height: 150px; overflow-y: auto;">
                            <!-- Selected orders will be listed here -->
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="bulkPickerId">Select Picker:</label>
                        <select class="form-control" id="bulkPickerId" name="picker_id" required>
                            <option value="">-- Select Picker --</option>
                            @foreach(\App\Models\User::where('role', 'picker')->where('status', 'active')->get() as $picker)
                                <option value="{{$picker->id}}">{{$picker->first_name}} {{$picker->last_name}}</option>
                            @endforeach
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmBulkAssign">Assign Orders</button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
  <link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.21.2/dist/sweetalert2.min.css" />
  <style>
      div.dataTables_wrapper div.dataTables_paginate{
          display: none;
      }
  </style>
@endpush

@push('scripts')

  <!-- Page level plugins -->
  <script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
  <script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.21.2/dist/sweetalert2.all.min.js"></script>

  <!-- Page level custom scripts -->
  <script src="{{asset('backend/js/demo/datatables-demo.js')}}"></script>
  <script>

      $('#order-dataTable').DataTable( {
            "columnDefs":[
                {
                    "orderable":false,
                    "targets":[0, 7] // Checkbox and Action columns
                }
            ]
        } );

        // Checkbox functionality
        $('#selectAll').change(function() {
            $('.order-checkbox:not(:disabled)').prop('checked', this.checked);
            toggleBulkAssignButton();
        });

        $('.order-checkbox').change(function() {
            toggleBulkAssignButton();
        });

        function toggleBulkAssignButton() {
            const checkedBoxes = $('.order-checkbox:checked').length;
            if (checkedBoxes > 0) {
                $('#bulkAssignBtn').show();
            } else {
                $('#bulkAssignBtn').hide();
            }
        }

        // Single order assignment
        $('.assign-picker-btn').click(function() {
            const orderId = $(this).data('order-id');
            const orderNumber = $(this).data('order-number');

            $('#assignOrderId').val(orderId);
            $('#assignOrderNumber').text(orderNumber);
            $('#assignPickerModal').modal('show');
        });

        $('#confirmAssignPicker').click(function() {
            const formData = {
                order_id: $('#assignOrderId').val(),
                picker_id: $('#assignPickerId').val()
            };

            if (!formData.picker_id) {
                Swal.fire('Error!', 'Please select a picker', 'error');
                return;
            }

            $.ajax({
                url: "{{route('orders.assign-to-picker')}}",
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.status) {
                        Swal.fire('Success!', response.message, 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('Error!', response.message, 'error');
                    }
                },
                error: function() {
                    Swal.fire('Error!', 'Something went wrong', 'error');
                }
            });
        });

        // Bulk assignment
        $('#bulkAssignBtn').click(function() {
            const selectedOrders = [];
            $('.order-checkbox:checked').each(function() {
                const row = $(this).closest('tr');
                const orderNumber = row.find('td:nth-child(2) strong').text();
                selectedOrders.push({
                    id: $(this).val(),
                    number: orderNumber
                });
            });

            let ordersList = '';
            selectedOrders.forEach(order => {
                ordersList += `<span class="badge badge-primary mr-1 mb-1">${order.number}</span>`;
            });

            $('#selectedOrdersList').html(ordersList);
            $('#bulkAssignModal').modal('show');
        });

        $('#confirmBulkAssign').click(function() {
            const orderIds = [];
            $('.order-checkbox:checked').each(function() {
                orderIds.push($(this).val());
            });

            const formData = {
                order_ids: orderIds,
                picker_id: $('#bulkPickerId').val()
            };

            if (!formData.picker_id) {
                Swal.fire('Error!', 'Please select a picker', 'error');
                return;
            }

            $.ajax({
                url: "{{route('orders.bulk-assign-to-picker')}}",
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.status) {
                        Swal.fire('Success!', response.message, 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('Error!', response.message, 'error');
                    }
                },
                error: function() {
                    Swal.fire('Error!', 'Something went wrong', 'error');
                }
            });
        });

        // Sweet alert

        function deleteData(id){

        }
  </script>
  <script>
      $(document).ready(function(){
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
          $('.dltBtn').click(function(e){
                var form=$(this).closest('form');
                var dataID=$(this).data('id');
                // alert(dataID);
                e.preventDefault();
                Swal.fire({
                    title: "Are you sure?",
                    text: "Once deleted, you will not be able to recover this data!",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Delete",
                    cancelButtonText: "Cancel",
                    dangerMode: true
                }).then((result) => {
                    if (result.isConfirmed) {
                        form.submit();
                    } else {
                        Swal.fire({
                            title: "Your data is safe!",
                            icon: "info",
                            confirmButtonText: "OK"
                        });
                    }
                });
          })
      })
  </script>
@endpush
