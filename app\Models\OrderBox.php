<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderBox extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'box_number',
        'box_label',
        'length',
        'width',
        'height',
        'weight',
        'max_weight',
        'is_full',
        'is_sealed',
        'notes',
        'shipping_info',
        'packed_at',
        'sealed_at'
    ];

    protected $casts = [
        'is_full' => 'boolean',
        'is_sealed' => 'boolean',
        'shipping_info' => 'array',
        'packed_at' => 'datetime',
        'sealed_at' => 'datetime'
    ];

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function items()
    {
        return $this->hasMany(Cart::class, 'box_id', 'id');
    }

    public function getVolumeAttribute()
    {
        if ($this->length && $this->width && $this->height) {
            return $this->length * $this->width * $this->height;
        }
        return null;
    }

    public function getRemainingWeightCapacityAttribute()
    {
        return $this->max_weight - ($this->weight ?? 0);
    }

    public function getItemCountAttribute()
    {
        return $this->items()->sum('quantity');
    }

    public function getTotalItemWeightAttribute()
    {
        return $this->items()->with('product')->get()->sum(function ($item) {
            return ($item->product->weight ?? 0) * $item->quantity;
        });
    }

    public function canAddItem($itemWeight = 0)
    {
        if ($this->is_full || $this->is_sealed) {
            return false;
        }

        $currentWeight = $this->weight ?? 0;
        return ($currentWeight + $itemWeight) <= $this->max_weight;
    }

    public function updateBoxLabel($totalBoxes)
    {
        $this->box_label = "Box {$this->box_number} of {$totalBoxes}";
        $this->save();
    }

    public function markAsFull()
    {
        $this->is_full = true;
        $this->packed_at = now();
        $this->save();
    }

    public function markAsSealed()
    {
        $this->is_sealed = true;
        $this->sealed_at = now();
        $this->save();
    }

    public function calculateShippingCost()
    {
        // Basic shipping cost calculation based on weight and dimensions
        $weight = $this->weight ?? 0;
        $volume = $this->getVolumeAttribute() ?? 0;
        
        // Dimensional weight (length × width × height ÷ 166 for domestic)
        $dimWeight = $volume / 166;
        
        // Use greater of actual weight or dimensional weight
        $billableWeight = max($weight, $dimWeight);
        
        // Basic rate calculation (this would integrate with actual shipping APIs)
        $baseRate = 5.00;
        $weightRate = $billableWeight * 0.50;
        
        return $baseRate + $weightRate;
    }

    public function getShippingDimensions()
    {
        return [
            'length' => $this->length,
            'width' => $this->width,
            'height' => $this->height,
            'weight' => $this->weight,
            'volume' => $this->getVolumeAttribute(),
            'dimensional_weight' => $this->getVolumeAttribute() ? $this->getVolumeAttribute() / 166 : 0
        ];
    }
}
