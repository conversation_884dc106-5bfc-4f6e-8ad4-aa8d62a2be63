<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('carts', function (Blueprint $table) {
            // Check if columns exist before adding them
            if (!Schema::hasColumn('carts', 'packed_quantity')) {
                $table->integer('packed_quantity')->nullable();
            }
            if (!Schema::hasColumn('carts', 'backorder_quantity')) {
                $table->integer('backorder_quantity')->default(0);
            }
            if (!Schema::hasColumn('carts', 'picker_notes')) {
                $table->text('picker_notes')->nullable();
            }
            if (!Schema::hasColumn('carts', 'packed_at')) {
                $table->timestamp('packed_at')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('carts', function (Blueprint $table) {
            $columns = ['packed_quantity', 'backorder_quantity', 'picker_notes', 'packed_at'];
            foreach ($columns as $column) {
                if (Schema::hasColumn('carts', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
