@extends('backend.layouts.master')

@section('title','Lamart || Picker Orders')

@section('main-content')
 <!-- DataTales Example -->
 <div class="card shadow mb-4">
     <div class="row">
         <div class="col-md-12">
            @include('backend.layouts.notification')
         </div>
     </div>
    <div class="card-header py-3">
      <h6 class="m-0 font-weight-bold text-primary float-left">My Assigned Orders</h6>
      <div class="float-right">
        <select id="statusFilter" class="form-control form-control-sm" style="width: auto; display: inline-block;">
          <option value="">All Orders</option>
          <option value="sent_to_warehouse">Sent to Warehouse</option>
          <option value="processing">Being Packed</option>
          <option value="shipped">Completed</option>
        </select>
      </div>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        @if(count($orders)>0)
        <table class="table table-bordered" id="order-dataTable" width="100%" cellspacing="0">
          <thead>
            <tr>
              <th>Order No.</th>
              <th>Customer</th>
              <th>Items</th>
              <th>Total Amount</th>
              <th>Delivery Method</th>
              <th>Status</th>
              <th>Assigned Date</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            @foreach($orders as $order)
                <tr data-status="{{$order->status}}">
                    <td>{{$order->order_number}}</td>
                    <td>{{$order->first_name.' '.$order->last_name}}</td>
                    <td>
                      <span class="badge badge-info">{{$order->cart_info->count()}} items</span>
                    </td>
                    <td>${{number_format($order->total_amount,2)}}</td>
                    <td>
                      <span class="badge badge-secondary">{{ucfirst($order->delivery_method ?? 'N/A')}}</span>
                    </td>
                    <td>
                        @if($order->status=='sent_to_warehouse')
                            <span class="badge badge-warning">Sent to Warehouse</span>
                        @elseif($order->status=='processing')
                            <span class="badge badge-info">Being Packed</span>
                        @elseif($order->status=='shipped')
                            <span class="badge badge-success">Completed</span>
                        @else
                            <span class="badge badge-primary">{{ucfirst($order->status)}}</span>
                        @endif
                    </td>
                    <td>{{$order->created_at->format('M d, Y')}}</td>
                    <td>
                        <a href="{{route('picker.orders.show',$order->id)}}" class="btn btn-primary btn-sm float-left mr-1" style="height:30px; width:30px;border-radius:50%" data-toggle="tooltip" title="view" data-placement="bottom"><i class="fas fa-eye"></i></a>
                        
                        @if($order->status == 'sent_to_warehouse')
                        <button class="btn btn-success btn-sm start-packing" data-id="{{$order->id}}" style="height:30px; width:auto;" data-toggle="tooltip" title="Start Packing" data-placement="bottom">
                          <i class="fas fa-play"></i> Start
                        </button>
                        @endif
                        
                        @if($order->status == 'processing')
                        <button class="btn btn-warning btn-sm continue-packing" data-id="{{$order->id}}" style="height:30px; width:auto;" data-toggle="tooltip" title="Continue Packing" data-placement="bottom">
                          <i class="fas fa-box"></i> Pack
                        </button>
                        @endif
                    </td>
                </tr>
            @endforeach
          </tbody>
        </table>
        <span style="float:right">{{$orders->links()}}</span>
        @else
          <h6 class="text-center">No orders assigned to you yet!</h6>
        @endif
      </div>
    </div>
</div>
@endsection

@push('styles')
  <link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" />
  <style>
      div.dataTables_wrapper div.dataTables_paginate{
          display: none;
      }
      .badge {
          font-size: 0.8em;
      }
  </style>
@endpush

@push('scripts')

  <!-- Page level plugins -->
  <script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
  <script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>

  <!-- Page level custom scripts -->
  <script src="{{asset('backend/js/demo/datatables-demo.js')}}"></script>
  <script>
      
      $('#order-dataTable').DataTable( {
            "columnDefs":[
                {
                    "orderable":false,
                    "targets":[7]
                }
            ]
        } );

        // Status filter functionality
        $('#statusFilter').on('change', function() {
            var status = $(this).val();
            if (status === '') {
                $('tbody tr').show();
            } else {
                $('tbody tr').hide();
                $('tbody tr[data-status="' + status + '"]').show();
            }
        });

        // Start packing functionality
        $('.start-packing').click(function(e) {
            e.preventDefault();
            var order_id = $(this).data('id');
            var token = $("meta[name='csrf-token']").attr("content");
            
            $.ajax({
                url: "{{route('picker.orders.start-packing')}}",
                type: 'POST',
                data: {
                    "_token": token,
                    "order_id": order_id
                },
                success: function(response) {
                    if(response.status) {
                        swal({
                            title: "Success!",
                            text: response.message,
                            type: "success"
                        }, function() {
                            location.reload();
                        });
                    } else {
                        swal("Error!", response.message, "error");
                    }
                },
                error: function() {
                    swal("Error!", "Something went wrong", "error");
                }
            });
        });

        // Continue packing functionality
        $('.continue-packing').click(function(e) {
            e.preventDefault();
            var order_id = $(this).data('id');
            window.location.href = "{{route('picker.orders.pack', '')}}" + "/" + order_id;
        });

  </script>
@endpush
