<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('carts', function (Blueprint $table) {
            $table->unsignedBigInteger('box_id')->nullable();
            $table->integer('packed_quantity')->nullable();
            $table->integer('backorder_quantity')->default(0);
            $table->text('picker_notes')->nullable();
            $table->timestamp('packed_at')->nullable();

            // Add foreign key constraint separately
            $table->index('box_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('carts', function (Blueprint $table) {
            $table->dropIndex(['box_id']);
            $table->dropColumn(['box_id', 'packed_quantity', 'backorder_quantity', 'picker_notes', 'packed_at']);
        });
    }
};
