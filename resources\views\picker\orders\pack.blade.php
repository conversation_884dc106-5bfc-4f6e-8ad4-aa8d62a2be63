@extends('backend.layouts.master')

@section('title','Lamart || Pack Order')

@section('main-content')
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Pack Order #{{$order->order_number}}</h5>
        <div>
            <span class="badge badge-info">{{$order->status}}</span>
            <a href="{{route('picker.orders.show', $order->id)}}" class="btn btn-secondary btn-sm ml-2">
                <i class="fas fa-arrow-left"></i> Back to Order
            </a>
        </div>
    </div>
    <div class="card-body">
        @if($order)
        
        <!-- Customer Info -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">Customer Information</h6>
                        <p class="mb-1"><strong>{{$order->first_name.' '.$order->last_name}}</strong></p>
                        <p class="mb-1">{{$order->email}}</p>
                        <p class="mb-1">{{$order->phone}}</p>
                        <p class="mb-0">{{$order->address1}}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">Order Summary</h6>
                        <p class="mb-1"><strong>Total Items:</strong> {{$order->cart_info->count()}}</p>
                        <p class="mb-1"><strong>Total Amount:</strong> ${{number_format($order->total_amount,2)}}</p>
                        <p class="mb-1"><strong>Delivery Method:</strong> {{ucfirst($order->delivery_method ?? 'N/A')}}</p>
                        <p class="mb-0"><strong>Order Date:</strong> {{$order->created_at->format('M d, Y')}}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Box Management -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">Box Management</h6>
                        <button class="btn btn-success btn-sm" id="addBoxBtn">
                            <i class="fas fa-plus"></i> Add Box
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="boxContainer">
                            <div class="box-item mb-2" data-box="1">
                                <span class="badge badge-primary">Box 1</span>
                                <button class="btn btn-sm btn-outline-primary ml-2 select-box" data-box="1">Select</button>
                            </div>
                        </div>
                        <input type="hidden" id="currentBox" value="1">
                        <p class="text-muted mt-2">Current Box: <span id="currentBoxDisplay">Box 1</span></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Items to Pack -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Items to Pack</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="packingTable">
                                <thead>
                                    <tr>
                                        <th>Item #</th>
                                        <th>Product</th>
                                        <th>Color</th>
                                        <th>Ordered Qty</th>
                                        <th>Pack Qty</th>
                                        <th>Box</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($order->cart_info as $item)
                                    <tr data-item-id="{{$item->id}}">
                                        <td>{{$item->product->item_number ?? 'N/A'}}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($item->product->photo)
                                                <img src="{{$item->product->photo}}" class="img-fluid mr-2" style="max-width:40px" alt="Product">
                                                @endif
                                                <div>
                                                    <strong>{{$item->product->title}}</strong><br>
                                                    <small class="text-muted">{{$item->product->summary}}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{$item->color_name->name ?? 'N/A'}}</td>
                                        <td><span class="badge badge-info">{{$item->quantity}}</span></td>
                                        <td>
                                            <input type="number" class="form-control form-control-sm pack-quantity" 
                                                   value="{{$item->quantity}}" min="0" max="{{$item->quantity}}" 
                                                   data-item-id="{{$item->id}}" style="width: 80px;">
                                        </td>
                                        <td>
                                            <select class="form-control form-control-sm box-select" data-item-id="{{$item->id}}" style="width: 80px;">
                                                <option value="1" {{$item->box == '1' ? 'selected' : ''}}>Box 1</option>
                                            </select>
                                        </td>
                                        <td>
                                            @if($item->is_packed)
                                                <span class="badge badge-success">Packed</span>
                                            @elseif($item->is_returned_by_picker)
                                                <span class="badge badge-danger">Returned</span>
                                            @else
                                                <span class="badge badge-warning">Pending</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-success btn-sm pack-item" data-item-id="{{$item->id}}" 
                                                        {{$item->is_packed ? 'disabled' : ''}}>
                                                    <i class="fas fa-check"></i> Pack
                                                </button>
                                                <button class="btn btn-warning btn-sm return-item" data-item-id="{{$item->id}}"
                                                        {{$item->is_returned_by_picker ? 'disabled' : ''}}>
                                                    <i class="fas fa-undo"></i> Return
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="d-flex justify-content-between">
                    <a href="{{route('picker.orders')}}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Orders
                    </a>
                    <div>
                        <button class="btn btn-info" onclick="printPackingList()">
                            <i class="fas fa-print"></i> Print Packing List
                        </button>
                        <button class="btn btn-success" id="completeOrderBtn">
                            <i class="fas fa-check-circle"></i> Complete Order
                        </button>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>
@endsection

@push('styles')
<style>
    .table td, .table th {
        vertical-align: middle;
    }
    .box-item {
        display: inline-block;
        margin-right: 10px;
    }
    .box-item.active {
        background-color: #e3f2fd;
        padding: 5px;
        border-radius: 5px;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
<script>
    let boxCount = 1;
    
    // Add new box
    $('#addBoxBtn').click(function() {
        boxCount++;
        const newBox = `
            <div class="box-item mb-2" data-box="${boxCount}">
                <span class="badge badge-primary">Box ${boxCount}</span>
                <button class="btn btn-sm btn-outline-primary ml-2 select-box" data-box="${boxCount}">Select</button>
            </div>
        `;
        $('#boxContainer').append(newBox);
        
        // Add option to all box selects
        $('.box-select').each(function() {
            $(this).append(`<option value="${boxCount}">Box ${boxCount}</option>`);
        });
    });
    
    // Select box
    $(document).on('click', '.select-box', function() {
        const boxNumber = $(this).data('box');
        $('#currentBox').val(boxNumber);
        $('#currentBoxDisplay').text(`Box ${boxNumber}`);
        
        $('.box-item').removeClass('active');
        $(this).closest('.box-item').addClass('active');
    });
    
    // Pack item
    $('.pack-item').click(function() {
        const itemId = $(this).data('item-id');
        const quantity = $(`.pack-quantity[data-item-id="${itemId}"]`).val();
        const box = $(`.box-select[data-item-id="${itemId}"]`).val();
        
        updateItemStatus(itemId, {
            is_packed: true,
            quantity: quantity,
            box: box
        }, $(this));
    });
    
    // Return item
    $('.return-item').click(function() {
        const itemId = $(this).data('item-id');
        
        updateItemStatus(itemId, {
            is_returned: true
        }, $(this));
    });
    
    // Update item status
    function updateItemStatus(itemId, data, button) {
        const token = $("meta[name='csrf-token']").attr("content");
        
        $.ajax({
            url: "{{route('picker.orders.update-item-status')}}",
            type: 'POST',
            data: {
                "_token": token,
                "item_id": itemId,
                ...data
            },
            success: function(response) {
                if(response.status) {
                    // Update UI
                    const row = button.closest('tr');
                    if(data.is_packed) {
                        row.find('td:nth-child(7)').html('<span class="badge badge-success">Packed</span>');
                        button.prop('disabled', true);
                    } else if(data.is_returned) {
                        row.find('td:nth-child(7)').html('<span class="badge badge-danger">Returned</span>');
                        button.prop('disabled', true);
                    }
                    
                    swal("Success!", response.message, "success");
                } else {
                    swal("Error!", response.message, "error");
                }
            },
            error: function() {
                swal("Error!", "Something went wrong", "error");
            }
        });
    }
    
    // Complete order
    $('#completeOrderBtn').click(function() {
        swal({
            title: "Complete Order?",
            text: "Are you sure you want to mark this order as completed?",
            type: "warning",
            showCancelButton: true,
            confirmButtonText: "Yes, complete it!"
        }, function() {
            // Implement order completion logic
            window.location.href = "{{route('picker.orders')}}";
        });
    });
    
    function printPackingList() {
        window.open("{{route('picker.orders.packing-list', $order->id)}}", '_blank');
    }
</script>
@endpush
