@extends('backend.layouts.master')

@section('title','Lamart || Pack Order')

@section('main-content')
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Pack Order #{{$order->order_number}}</h5>
        <div>
            <span class="badge badge-info">{{$order->status}}</span>
            <a href="{{route('picker.orders.show', $order->id)}}" class="btn btn-secondary btn-sm ml-2">
                <i class="fas fa-arrow-left"></i> Back to Order
            </a>
        </div>
    </div>
    <div class="card-body">
        @if($order)

        <!-- Customer Info -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">Customer Information</h6>
                        <p class="mb-1"><strong>{{$order->first_name.' '.$order->last_name}}</strong></p>
                        <p class="mb-1">{{$order->email}}</p>
                        <p class="mb-1">{{$order->phone}}</p>
                        <p class="mb-0">{{$order->address1}}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">Order Summary</h6>
                        <p class="mb-1"><strong>Total Items:</strong> {{$order->cart_info->count()}}</p>
                        <p class="mb-1"><strong>Total Amount:</strong> ${{number_format($order->total_amount,2)}}</p>
                        <p class="mb-1"><strong>Delivery Method:</strong> {{ucfirst($order->delivery_method ?? 'N/A')}}</p>
                        <p class="mb-0"><strong>Order Date:</strong> {{$order->created_at->format('M d, Y')}}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Box Management -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">Box Management</h6>
                        <div>
                            <button class="btn btn-success btn-sm" id="addBoxBtn">
                                <i class="fas fa-plus"></i> Add Box
                            </button>
                            <button class="btn btn-info btn-sm" id="printAllLabelsBtn">
                                <i class="fas fa-print"></i> Print All Labels
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row" id="boxContainer">
                            @foreach($order->boxes as $box)
                            <div class="col-md-4 mb-3">
                                <div class="card box-card" data-box-id="{{$box->id}}">
                                    <div class="card-header d-flex justify-content-between align-items-center p-2">
                                        <small class="font-weight-bold">{{$box->box_label}}</small>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary select-box" data-box-id="{{$box->id}}">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-outline-info print-box-label" data-box-id="{{$box->id}}">
                                                <i class="fas fa-print"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body p-2">
                                        <div class="row">
                                            <div class="col-6">
                                                <small class="text-muted">Dimensions (L×W×H):</small>
                                                <div class="input-group input-group-sm mb-1">
                                                    <input type="number" class="form-control box-dimension"
                                                           data-box-id="{{$box->id}}" data-field="length"
                                                           placeholder="L" value="{{$box->length}}" step="0.1">
                                                    <input type="number" class="form-control box-dimension"
                                                           data-box-id="{{$box->id}}" data-field="width"
                                                           placeholder="W" value="{{$box->width}}" step="0.1">
                                                    <input type="number" class="form-control box-dimension"
                                                           data-box-id="{{$box->id}}" data-field="height"
                                                           placeholder="H" value="{{$box->height}}" step="0.1">
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">Weight (lbs):</small>
                                                <input type="number" class="form-control form-control-sm box-weight"
                                                       data-box-id="{{$box->id}}" value="{{$box->weight}}"
                                                       placeholder="0.0" step="0.1">
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <small class="text-muted">Items: </small>
                                            <span class="badge badge-info">{{$box->items->sum('packed_quantity')}} items</span>
                                            @if($box->is_full)
                                                <span class="badge badge-success">Full</span>
                                            @endif
                                            @if($box->is_sealed)
                                                <span class="badge badge-dark">Sealed</span>
                                            @endif
                                        </div>
                                        <div class="mt-2">
                                            <button class="btn btn-sm btn-warning mark-full" data-box-id="{{$box->id}}"
                                                    {{$box->is_full ? 'disabled' : ''}}>
                                                <i class="fas fa-box"></i> Mark Full
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                        <input type="hidden" id="currentBoxId" value="{{$order->boxes->first()->id ?? ''}}">
                        <p class="text-muted mt-2">
                            Current Box: <span id="currentBoxDisplay">{{$order->boxes->first()->box_label ?? 'None'}}</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Items to Pack -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Items to Pack</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="packingTable">
                                <thead>
                                    <tr>
                                        <th>Item #</th>
                                        <th>Product</th>
                                        <th>Color</th>
                                        <th>Ordered</th>
                                        <th>Available</th>
                                        <th>Pack Qty</th>
                                        <th>Backorder</th>
                                        <th>Box</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($order->cart_info as $item)
                                    @php
                                        $availableQty = $item->getAvailableQuantityAttribute();
                                        $packedQty = $item->packed_quantity ?? 0;
                                        $backorderQty = $item->backorder_quantity ?? 0;
                                    @endphp
                                    <tr data-item-id="{{$item->id}}" class="{{$availableQty < $item->quantity ? 'table-warning' : ''}}">
                                        <td>{{$item->product->item_number ?? 'N/A'}}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($item->product->photo)
                                                <img src="{{$item->product->photo}}" class="img-fluid mr-2" style="max-width:40px" alt="Product">
                                                @endif
                                                <div>
                                                    <strong>{{$item->product->title}}</strong><br>
                                                    <small class="text-muted">{{$item->product->summary}}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{$item->color_name->name ?? 'N/A'}}</td>
                                        <td><span class="badge badge-info">{{$item->quantity}}</span></td>
                                        <td>
                                            <span class="badge {{$availableQty >= $item->quantity ? 'badge-success' : 'badge-danger'}}">
                                                {{$availableQty}}
                                            </span>
                                            @if($availableQty < $item->quantity)
                                                <small class="text-danger d-block">⚠️ Low Stock</small>
                                            @endif
                                        </td>
                                        <td>
                                            <input type="number" class="form-control form-control-sm pack-quantity"
                                                   value="{{$packedQty ?: min($item->quantity, $availableQty)}}"
                                                   min="0" max="{{min($item->quantity, $availableQty)}}"
                                                   data-item-id="{{$item->id}}" style="width: 80px;">
                                        </td>
                                        <td>
                                            @if($backorderQty > 0)
                                                <span class="badge badge-warning">{{$backorderQty}}</span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <select class="form-control form-control-sm box-select" data-item-id="{{$item->id}}" style="width: 100px;">
                                                @foreach($order->boxes as $box)
                                                    <option value="{{$box->id}}" {{$item->box_id == $box->id ? 'selected' : ''}}>
                                                        {{$box->box_label}}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </td>
                                        <td>
                                            @if($item->is_packed)
                                                <span class="badge badge-success">Packed</span>
                                            @elseif($item->is_returned_by_picker)
                                                <span class="badge badge-danger">Returned</span>
                                            @elseif($backorderQty > 0)
                                                <span class="badge badge-warning">Partial</span>
                                            @else
                                                <span class="badge badge-secondary">Pending</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group-vertical btn-group-sm" role="group">
                                                <button class="btn btn-success btn-sm pack-item mb-1" data-item-id="{{$item->id}}"
                                                        {{$item->is_packed ? 'disabled' : ''}}>
                                                    <i class="fas fa-check"></i> Pack
                                                </button>
                                                @if($availableQty < $item->quantity)
                                                <button class="btn btn-warning btn-sm create-backorder mb-1" data-item-id="{{$item->id}}"
                                                        data-shortage="{{$item->quantity - $availableQty}}">
                                                    <i class="fas fa-exclamation-triangle"></i> Backorder
                                                </button>
                                                @endif
                                                <button class="btn btn-danger btn-sm return-item" data-item-id="{{$item->id}}"
                                                        {{$item->is_returned_by_picker ? 'disabled' : ''}}>
                                                    <i class="fas fa-undo"></i> Return
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="d-flex justify-content-between">
                    <a href="{{route('picker.orders')}}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Orders
                    </a>
                    <div>
                        <button class="btn btn-info" onclick="printPackingList()">
                            <i class="fas fa-print"></i> Print Packing List
                        </button>
                        <button class="btn btn-success" id="completeOrderBtn">
                            <i class="fas fa-check-circle"></i> Complete Order
                        </button>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>
@endsection

@push('styles')
<style>
    .table td, .table th {
        vertical-align: middle;
    }
    .box-item {
        display: inline-block;
        margin-right: 10px;
    }
    .box-item.active {
        background-color: #e3f2fd;
        padding: 5px;
        border-radius: 5px;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
<script>
    // Add new box
    $('#addBoxBtn').click(function() {
        const token = $("meta[name='csrf-token']").attr("content");

        $.ajax({
            url: "{{route('picker.orders.create-box')}}",
            type: 'POST',
            data: {
                "_token": token,
                "order_id": {{$order->id}},
                "max_weight": 50
            },
            success: function(response) {
                if(response.status) {
                    location.reload(); // Reload to show new box
                } else {
                    Swal.fire('Error!', response.message, 'error');
                }
            },
            error: function() {
                Swal.fire('Error!', 'Failed to create box', 'error');
            }
        });
    });

    // Select box
    $(document).on('click', '.select-box', function() {
        const boxId = $(this).data('box-id');
        const boxLabel = $(this).closest('.box-card').find('.font-weight-bold').text();

        $('#currentBoxId').val(boxId);
        $('#currentBoxDisplay').text(boxLabel);

        $('.box-card').removeClass('border-primary');
        $(this).closest('.box-card').addClass('border-primary');

        // Update all box selects to this box
        $('.box-select').val(boxId);
    });

    // Update box dimensions and weight
    $(document).on('change', '.box-dimension, .box-weight', function() {
        const boxId = $(this).data('box-id');
        const field = $(this).data('field') || 'weight';
        const value = $(this).val();
        const token = $("meta[name='csrf-token']").attr("content");

        let data = {
            "_token": token
        };
        data[field] = value;

        $.ajax({
            url: `/picker/orders/boxes/${boxId}/update`,
            type: 'PUT',
            data: data,
            success: function(response) {
                if(!response.status) {
                    Swal.fire('Error!', response.message, 'error');
                }
            }
        });
    });

    // Mark box as full
    $(document).on('click', '.mark-full', function() {
        const boxId = $(this).data('box-id');
        const token = $("meta[name='csrf-token']").attr("content");

        $.ajax({
            url: `/picker/orders/boxes/${boxId}/update`,
            type: 'PUT',
            data: {
                "_token": token,
                "is_full": true
            },
            success: function(response) {
                if(response.status) {
                    location.reload();
                } else {
                    Swal.fire('Error!', response.message, 'error');
                }
            }
        });
    });

    // Print box label
    $(document).on('click', '.print-box-label', function() {
        const boxId = $(this).data('box-id');
        window.open(`/picker/orders/boxes/${boxId}/label`, '_blank');
    });

    // Print all labels
    $('#printAllLabelsBtn').click(function() {
        @foreach($order->boxes as $box)
            window.open(`/picker/orders/boxes/{{$box->id}}/label`, '_blank');
        @endforeach
    });

    // Pack item
    $(document).on('click', '.pack-item', function() {
        const itemId = $(this).data('item-id');
        const packedQuantity = $(`.pack-quantity[data-item-id="${itemId}"]`).val();
        const boxId = $(`.box-select[data-item-id="${itemId}"]`).val();

        updateItemStatus(itemId, {
            is_packed: true,
            packed_quantity: packedQuantity,
            box_id: boxId
        }, $(this));
    });

    // Return item
    $(document).on('click', '.return-item', function() {
        const itemId = $(this).data('item-id');

        updateItemStatus(itemId, {
            is_returned: true
        }, $(this));
    });

    // Create backorder
    $(document).on('click', '.create-backorder', function() {
        const itemId = $(this).data('item-id');
        const shortage = $(this).data('shortage');

        Swal.fire({
            title: 'Create Backorder?',
            text: `This will create a backorder for ${shortage} items due to insufficient stock.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, create backorder',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                const token = $("meta[name='csrf-token']").attr("content");

                $.ajax({
                    url: "{{route('picker.orders.create-backorder')}}",
                    type: 'POST',
                    data: {
                        "_token": token,
                        "item_id": itemId,
                        "backorder_quantity": shortage
                    },
                    success: function(response) {
                        if(response.status) {
                            Swal.fire('Success!', response.message, 'success').then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire('Error!', response.message, 'error');
                        }
                    },
                    error: function() {
                        Swal.fire('Error!', 'Failed to create backorder', 'error');
                    }
                });
            }
        });
    });

    // Update item status
    function updateItemStatus(itemId, data, button) {
        const token = $("meta[name='csrf-token']").attr("content");

        $.ajax({
            url: "{{route('picker.orders.update-item-status')}}",
            type: 'POST',
            data: {
                "_token": token,
                "item_id": itemId,
                ...data
            },
            success: function(response) {
                if(response.status) {
                    // Update UI
                    const row = button.closest('tr');
                    if(data.is_packed) {
                        row.find('td:nth-child(7)').html('<span class="badge badge-success">Packed</span>');
                        button.prop('disabled', true);
                    } else if(data.is_returned) {
                        row.find('td:nth-child(7)').html('<span class="badge badge-danger">Returned</span>');
                        button.prop('disabled', true);
                    }

                    swal("Success!", response.message, "success");
                } else {
                    swal("Error!", response.message, "error");
                }
            },
            error: function() {
                swal("Error!", "Something went wrong", "error");
            }
        });
    }

    // Complete order
    $('#completeOrderBtn').click(function() {
        // First check order progress
        $.get(`/picker/orders/{{$order->id}}/progress`, function(response) {
            if (response.status && response.progress.can_complete) {
                Swal.fire({
                    title: 'Complete Order?',
                    html: `
                        <div class="text-left">
                            <p><strong>Order Summary:</strong></p>
                            <ul>
                                <li>Total Items: ${response.progress.total_items}</li>
                                <li>Packed Items: ${response.progress.packed_items}</li>
                                <li>Backorder Items: ${response.progress.backorder_items}</li>
                                <li>Total Boxes: ${response.progress.total_boxes}</li>
                                <li>Progress: ${response.progress.progress_percentage}%</li>
                            </ul>
                            <p>This will mark the order as shipped and seal all boxes.</p>
                        </div>
                    `,
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: 'Yes, complete order!',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        completeOrder();
                    }
                });
            } else {
                Swal.fire({
                    title: 'Cannot Complete Order',
                    text: 'Some items are not packed or backordered. Please complete all items before finishing the order.',
                    icon: 'error'
                });
            }
        });
    });

    function completeOrder() {
        const token = $("meta[name='csrf-token']").attr("content");

        $.ajax({
            url: `/picker/orders/{{$order->id}}/complete`,
            type: 'POST',
            data: {
                "_token": token
            },
            success: function(response) {
                if(response.status) {
                    Swal.fire({
                        title: 'Order Completed!',
                        html: `
                            <div class="text-left">
                                <p>Order has been successfully completed:</p>
                                <ul>
                                    <li>Status: ${response.order_status}</li>
                                    <li>Total Boxes: ${response.total_boxes}</li>
                                    <li>Backorders Created: ${response.has_backorders ? 'Yes' : 'No'}</li>
                                </ul>
                            </div>
                        `,
                        icon: 'success'
                    }).then(() => {
                        window.location.href = "{{route('picker.orders')}}";
                    });
                } else {
                    Swal.fire('Error!', response.message, 'error');
                }
            },
            error: function() {
                Swal.fire('Error!', 'Failed to complete order', 'error');
            }
        });
    }

    function printPackingList() {
        window.open("{{route('picker.orders.packing-list', $order->id)}}", '_blank');
    }
</script>
@endpush
