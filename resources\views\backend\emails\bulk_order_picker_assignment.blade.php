<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multiple Orders Assignment</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: linear-gradient(135deg, #4D734E, #5A5863);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 0 0 8px 8px;
            border: 1px solid #ddd;
        }
        .orders-summary {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #4D734E;
        }
        .order-item {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 3px solid #17a2b8;
        }
        .btn {
            display: inline-block;
            background: #4D734E;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #3a5a3c;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 14px;
        }
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #ddd;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #4D734E;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏭 LAMART</h1>
        <h2>Multiple Orders Assignment</h2>
    </div>

    <div class="content">
        <h3>Hello {{$picker->first_name}} {{$picker->last_name}},</h3>
        
        <p>You have been assigned <strong>{{$count}} new orders</strong> for picking and packing. All orders have been sent to the warehouse and are ready for processing.</p>

        <div class="highlight">
            <strong>⚡ Priority Assignment:</strong> {{$count}} orders are now in your queue and ready for immediate processing.
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{$count}}</div>
                <div class="stat-label">Total Orders</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{$orders->sum(function($order) { return $order->cart_info->count(); })}}</div>
                <div class="stat-label">Total Items</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${{number_format($orders->sum('total_amount'), 0)}}</div>
                <div class="stat-label">Total Value</div>
            </div>
        </div>

        <div class="orders-summary">
            <h4>📦 Assigned Orders</h4>
            <p><strong>Assigned By:</strong> {{$assigned_by->first_name}} {{$assigned_by->last_name}}</p>
            <p><strong>Assignment Date:</strong> {{now()->format('M d, Y h:i A')}}</p>
            
            @foreach($orders as $order)
            <div class="order-item">
                <table>
                    <tr>
                        <th style="width: 30%;">Order #:</th>
                        <td><strong>{{$order->order_number}}</strong></td>
                    </tr>
                    <tr>
                        <th>Customer:</th>
                        <td>{{$order->first_name}} {{$order->last_name}}</td>
                    </tr>
                    <tr>
                        <th>Items:</th>
                        <td>{{$order->cart_info->count()}} items</td>
                    </tr>
                    <tr>
                        <th>Total:</th>
                        <td>${{number_format($order->total_amount, 2)}}</td>
                    </tr>
                    <tr>
                        <th>Delivery:</th>
                        <td>{{ucfirst($order->delivery_method ?? 'Standard')}}</td>
                    </tr>
                    @if($order->delivery_instructions)
                    <tr>
                        <th>Instructions:</th>
                        <td style="font-style: italic; color: #666;">{{$order->delivery_instructions}}</td>
                    </tr>
                    @endif
                </table>
            </div>
            @endforeach
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{{url('/picker/orders')}}" class="btn">
                📦 View All My Orders
            </a>
            <a href="{{url('/picker')}}" class="btn" style="background: #17a2b8;">
                🏠 Go to Dashboard
            </a>
        </div>

        <div class="highlight">
            <h4>📋 Recommended Workflow:</h4>
            <ol>
                <li><strong>Prioritize by delivery method:</strong> Process urgent/express orders first</li>
                <li><strong>Group similar items:</strong> Pick items efficiently by location</li>
                <li><strong>Update status regularly:</strong> Mark items as packed to track progress</li>
                <li><strong>Quality check:</strong> Verify all items before final packing</li>
                <li><strong>Generate packing slips:</strong> Print labels and documentation</li>
            </ol>
        </div>

        <div style="background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h4 style="color: #2d5a2d; margin-top: 0;">💡 Pro Tips:</h4>
            <ul style="margin-bottom: 0;">
                <li>Use the bulk actions to efficiently manage multiple orders</li>
                <li>Check inventory levels before starting to avoid delays</li>
                <li>Contact the office immediately if any items are out of stock</li>
                <li>Take photos of any damaged items before processing returns</li>
            </ul>
        </div>

        <p><strong>Important:</strong> All {{$count}} orders are now in "Sent to Warehouse" status and ready for immediate processing. Please maintain our quality standards and update the system as you complete each step.</p>
    </div>

    <div class="footer">
        <p>This is an automated notification from the Lamart Order Management System.</p>
        <p>For questions or support, please contact your supervisor or the office.</p>
        <p>&copy; {{date('Y')}} Lamart. All rights reserved.</p>
    </div>
</body>
</html>
