<?php

namespace App\Http\Controllers\Picker;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Support\Facades\Auth;

class OrderController extends Controller
{
    public function index(Request $request){
        $query = Order::where('picker_id', Auth::user()->id)
                     ->with(['cart_info', 'user']);

        // Apply status filter if provided
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        $orders = $query->latest()->paginate(10);

        // Preserve query parameters in pagination
        $orders->appends($request->query());

        return view('picker.orders.index', compact('orders'));
    }

    public function show($id)
    {
        $order = Order::with(['cart_info.product', 'cart_info.color_name', 'user'])
                     ->where('picker_id', Auth::user()->id)
                     ->findOrFail($id);
        return view('picker.orders.show', compact('order'));
    }

    public function startPacking(Request $request)
    {
        try {
            $order = Order::where('picker_id', Auth::user()->id)
                         ->where('id', $request->order_id)
                         ->where('status', 'sent_to_warehouse')
                         ->firstOrFail();

            $order->status = 'processing';
            $order->save();

            return response()->json([
                'status' => true,
                'message' => 'Order packing started successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to start packing: ' . $e->getMessage()
            ]);
        }
    }

    public function pack($id)
    {
        $order = Order::with([
            'cart_info' => function($query) {
                $query->with(['product', 'color_name', 'orderBox'])
                      ->where('status', '!=', 'distributed'); // Exclude distributed items
            },
            'boxes.items' => function($query) {
                $query->with(['product', 'color_name'])->where('is_packed', true);
            },
            'user'
        ])->where('picker_id', Auth::user()->id)
          ->where('status', 'processing')
          ->findOrFail($id);

        // Ensure at least one box exists (only create if truly none exist)
        $existingBoxes = $order->boxes()->count();
        \Log::info('Checking existing boxes for order', [
            'order_id' => $order->id,
            'existing_boxes_count' => $existingBoxes
        ]);

        if ($existingBoxes == 0) {
            \Log::info('Creating initial box for order', ['order_id' => $order->id]);
            $this->createInitialBox($order);
        }

        return view('picker.orders.pack', compact('order'));
    }

    private function createInitialBox($order)
    {
        // Double-check that no boxes exist to prevent race conditions
        $existingBoxes = $order->boxes()->count();
        if ($existingBoxes > 0) {
            \Log::warning('Attempted to create initial box but boxes already exist', [
                'order_id' => $order->id,
                'existing_boxes' => $existingBoxes
            ]);
            return $order->boxes()->first();
        }

        $box = \App\Models\OrderBox::create([
            'order_id' => $order->id,
            'box_number' => 1,
            'box_label' => 'Box 1 of 1',
            'length' => 12,
            'width' => 8,
            'height' => 6,
            'weight' => 0,
            'max_weight' => 50.00,
            'is_full' => false,
            'is_sealed' => false
        ]);

        \Log::info('Created initial box', [
            'order_id' => $order->id,
            'box_id' => $box->id
        ]);

        return $box;
    }

    public function packingList($id)
    {
        $order = Order::with(['cart_info.product', 'cart_info.color_name', 'user'])
                     ->where('picker_id', Auth::user()->id)
                     ->findOrFail($id);

        return view('picker.orders.packing-list', compact('order'));
    }

    public function updateItemStatus(Request $request)
    {
        try {
            $request->validate([
                'item_id' => 'required|exists:carts,id',
                'is_packed' => 'nullable|in:true,false,1,0',
                'is_returned' => 'nullable|in:true,false,1,0',
                'box_id' => 'nullable|exists:order_boxes,id',
                'packed_quantity' => 'nullable|integer|min:0',
                'picker_notes' => 'nullable|string'
            ]);

            $item = \App\Models\Cart::findOrFail($request->item_id);

            // Verify the item belongs to an order assigned to this picker
            $order = Order::where('id', $item->order_id)
                         ->where('picker_id', Auth::user()->id)
                         ->firstOrFail();

            // Handle quantity and backorder logic
            if ($request->has('packed_quantity')) {
                $packedQty = $request->packed_quantity;
                $originalQty = $item->quantity;

                if ($packedQty > $originalQty) {
                    return response()->json([
                        'status' => false,
                        'message' => 'Packed quantity cannot exceed ordered quantity'
                    ]);
                }

                // Check if we have enough stock
                if (!$item->canFulfillQuantity($packedQty)) {
                    $availableQty = $item->getAvailableQuantityAttribute();
                    $backorderQty = $originalQty - $availableQty;

                    if ($availableQty > 0) {
                        // Partial fulfillment
                        $item->packed_quantity = $availableQty;
                        $item->createBackorder($backorderQty);

                        return response()->json([
                            'status' => true,
                            'message' => "Partial fulfillment: {$availableQty} packed, {$backorderQty} backordered",
                            'backorder_created' => true,
                            'packed_quantity' => $availableQty,
                            'backorder_quantity' => $backorderQty
                        ]);
                    } else {
                        // Full backorder
                        $item->createBackorder($originalQty);

                        return response()->json([
                            'status' => true,
                            'message' => "Item fully backordered due to insufficient stock",
                            'backorder_created' => true,
                            'backorder_quantity' => $originalQty
                        ]);
                    }
                } else {
                    $item->packed_quantity = $packedQty;

                    // Create backorder for remaining quantity if any
                    if ($packedQty < $originalQty) {
                        $backorderQty = $originalQty - $packedQty;
                        $item->createBackorder($backorderQty);
                    }
                }
            }

            if ($request->has('is_packed')) {
                $isPacked = filter_var($request->is_packed, FILTER_VALIDATE_BOOLEAN);
                $item->is_packed = $isPacked;
                if ($isPacked) {
                    $item->packed_at = now();
                }
            }

            if ($request->has('is_returned')) {
                $isReturned = filter_var($request->is_returned, FILTER_VALIDATE_BOOLEAN);
                $item->is_returned_by_picker = $isReturned;
            }

            if ($request->has('box_id')) {
                $item->box_id = $request->box_id;

                // Update the old box field for backward compatibility
                if ($request->box_id) {
                    $box = \App\Models\OrderBox::find($request->box_id);
                    $item->box = $box ? $box->box_number : null;
                }
            }

            if ($request->has('picker_notes')) {
                $item->picker_notes = $request->picker_notes;
            }

            $item->save();

            return response()->json([
                'status' => true,
                'message' => 'Item status updated successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to update item: ' . $e->getMessage()
            ]);
        }
    }

    public function createBox(Request $request)
    {
        try {
            $request->validate([
                'order_id' => 'required|exists:orders,id',
                'length' => 'nullable|numeric|min:0',
                'width' => 'nullable|numeric|min:0',
                'height' => 'nullable|numeric|min:0',
                'max_weight' => 'nullable|numeric|min:0|max:200'
            ]);

            $order = Order::where('id', $request->order_id)
                         ->where('picker_id', Auth::user()->id)
                         ->firstOrFail();

            $nextBoxNumber = $order->boxes()->max('box_number') + 1;
            $totalBoxes = $order->boxes()->count() + 1; // Include the new box we're creating

            \Log::info('Creating new box', [
                'order_id' => $order->id,
                'next_box_number' => $nextBoxNumber,
                'total_boxes_after_creation' => $totalBoxes
            ]);

            $box = \App\Models\OrderBox::create([
                'order_id' => $order->id,
                'box_number' => $nextBoxNumber,
                'box_label' => "Box {$nextBoxNumber} of {$totalBoxes}",
                'length' => $request->length ?? 12,
                'width' => $request->width ?? 8,
                'height' => $request->height ?? 6,
                'weight' => 0,
                'max_weight' => $request->max_weight ?? 50.00,
                'is_full' => false,
                'is_sealed' => false
            ]);

            // Update all existing box labels to reflect the new total
            $this->updateBoxLabels($order);

            \Log::info('Box created successfully', [
                'box_id' => $box->id,
                'order_id' => $order->id,
                'box_number' => $box->box_number,
                'box_label' => $box->box_label
            ]);

            // Ensure all required fields are present in the response
            $boxData = [
                'id' => $box->id,
                'order_id' => $box->order_id,
                'box_number' => $box->box_number,
                'box_label' => $box->box_label,
                'length' => $box->length,
                'width' => $box->width,
                'height' => $box->height,
                'weight' => $box->weight,
                'max_weight' => $box->max_weight,
                'is_full' => $box->is_full,
                'is_sealed' => $box->is_sealed
            ];

            return response()->json([
                'status' => true,
                'message' => 'Box created successfully!',
                'box' => $boxData
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'status' => false,
                'message' => 'Validation error: ' . implode(', ', $e->validator->errors()->all())
            ]);
        } catch (\Exception $e) {
            \Log::error('Error creating box', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'status' => false,
                'message' => 'Failed to create box: ' . $e->getMessage()
            ]);
        }
    }

    public function updateBox(Request $request, $boxId)
    {
        try {
            \Log::info('Box update request received', [
                'box_id' => $boxId,
                'request_data' => $request->all(),
                'url' => $request->url()
            ]);

            $request->validate([
                'length' => 'nullable|numeric|min:0',
                'width' => 'nullable|numeric|min:0',
                'height' => 'nullable|numeric|min:0',
                'weight' => 'nullable|numeric|min:0',
                'max_weight' => 'nullable|numeric|min:0|max:200',
                'is_full' => 'nullable|in:true,false,1,0',
                'notes' => 'nullable|string'
            ]);

            // Check if boxId is valid
            if (!$boxId || $boxId === 'undefined' || $boxId === 'null') {
                \Log::error('Invalid box ID received', ['box_id' => $boxId]);
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid box ID: ' . $boxId
                ]);
            }

            // Check if box exists
            $box = \App\Models\OrderBox::find($boxId);
            if (!$box) {
                \Log::error('Box not found in database', ['box_id' => $boxId]);
                return response()->json([
                    'status' => false,
                    'message' => 'Box not found with ID: ' . $boxId
                ]);
            }

            // Verify the box belongs to an order assigned to this picker
            $order = Order::where('id', $box->order_id)
                         ->where('picker_id', Auth::user()->id)
                         ->first();

            if (!$order) {
                return response()->json([
                    'status' => false,
                    'message' => 'Box does not belong to your assigned orders'
                ]);
            }

            // Prepare update data with proper boolean conversion
            $updateData = $request->only([
                'length', 'width', 'height', 'weight', 'max_weight', 'notes'
            ]);

            // Handle is_full boolean conversion
            if ($request->has('is_full')) {
                $updateData['is_full'] = filter_var($request->is_full, FILTER_VALIDATE_BOOLEAN);
            }

            $box->update($updateData);

            if ($request->has('is_full') && filter_var($request->is_full, FILTER_VALIDATE_BOOLEAN)) {
                $box->markAsFull();
            }

            return response()->json([
                'status' => true,
                'message' => 'Box updated successfully!',
                'box' => $box->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to update box: ' . $e->getMessage()
            ]);
        }
    }

    private function updateBoxLabels($order)
    {
        $boxes = $order->boxes()->orderBy('box_number')->get();
        $totalBoxes = $boxes->count();

        foreach ($boxes as $box) {
            $box->updateBoxLabel($totalBoxes);
        }
    }

    public function printBoxLabel($boxId)
    {
        try {
            $box = \App\Models\OrderBox::with([
                'order.user',
                'items' => function($query) {
                    $query->with(['product', 'color_name'])->where('is_packed', true);
                }
            ])->findOrFail($boxId);

            // Verify the box belongs to an order assigned to this picker
            $order = Order::where('id', $box->order_id)
                         ->where('picker_id', Auth::user()->id)
                         ->firstOrFail();

            // Debug: Log the items to see what's being loaded
            \Log::info('Box items for label:', [
                'box_id' => $boxId,
                'items_count' => $box->items->count(),
                'items' => $box->items->map(function($item) {
                    return [
                        'id' => $item->id,
                        'product_id' => $item->product_id,
                        'product_title' => $item->product->title ?? 'No Product',
                        'product_item_number' => $item->product->item_number ?? 'No Item Number',
                        'quantity' => $item->quantity,
                        'packed_quantity' => $item->packed_quantity
                    ];
                })
            ]);

            return view('picker.orders.box-label', compact('box', 'order'));
        } catch (\Exception $e) {
            \Log::error('Failed to generate box label: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to generate box label: ' . $e->getMessage()
            ]);
        }
    }

    public function printAllLabels($orderId)
    {
        try {
            $order = Order::with([
                'user',
                'boxes' => function($query) {
                    $query->with([
                        'items' => function($itemQuery) {
                            $itemQuery->with(['product', 'color_name'])->where('is_packed', true);
                        }
                    ]);
                }
            ])->where('id', $orderId)
              ->where('picker_id', Auth::user()->id)
              ->firstOrFail();

            $boxes = $order->boxes;

            if ($boxes->isEmpty()) {
                return redirect()->back()->with('error', 'No boxes found for this order');
            }

            return view('picker.orders.all-labels', compact('boxes', 'order'));
        } catch (\Exception $e) {
            \Log::error('Failed to generate all labels: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to generate labels: ' . $e->getMessage());
        }
    }

    public function completeOrder(Request $request, $orderId)
    {
        try {
            $order = Order::with(['cart_info', 'boxes'])
                         ->where('id', $orderId)
                         ->where('picker_id', Auth::user()->id)
                         ->where('status', 'processing')
                         ->firstOrFail();

            // Check if order can be completed
            if (!$order->canBeCompleted()) {
                return response()->json([
                    'status' => false,
                    'message' => 'Order cannot be completed. Some items are not packed or backordered.'
                ]);
            }

            // Complete the order
            $order->completeOrder();

            // Log the completion
            \App\Models\OrderHistory::create([
                'order_id' => $order->id,
                'user_id' => Auth::user()->id,
                'customer_id' => $order->user_id,
                'action' => 'completed_packing',
                'details' => [
                    'completed_by' => Auth::user()->first_name . ' ' . Auth::user()->last_name,
                    'total_boxes' => $order->boxes()->count(),
                    'packing_progress' => $order->getPackingProgressAttribute(),
                    'backorders_created' => $order->hasBackorders()
                ]
            ]);

            return response()->json([
                'status' => true,
                'message' => 'Order completed successfully!',
                'order_status' => $order->status,
                'total_boxes' => $order->total_boxes,
                'has_backorders' => $order->hasBackorders()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to complete order: ' . $e->getMessage()
            ]);
        }
    }

    public function getOrderProgress($orderId)
    {
        try {
            $order = Order::with(['cart_info', 'boxes'])
                         ->where('id', $orderId)
                         ->where('picker_id', Auth::user()->id)
                         ->firstOrFail();

            $totalItems = $order->cart_info->sum('quantity');
            $packedItems = $order->cart_info->sum('packed_quantity');
            $backorderedItems = $order->cart_info->sum('backorder_quantity');

            return response()->json([
                'status' => true,
                'progress' => [
                    'total_items' => $totalItems,
                    'packed_items' => $packedItems,
                    'backorder_items' => $backorderedItems,
                    'progress_percentage' => $order->getPackingProgressAttribute(),
                    'total_boxes' => $order->total_boxes,
                    'can_complete' => $order->canBeCompleted()
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to get order progress: ' . $e->getMessage()
            ]);
        }
    }

    public function createBackorder(Request $request)
    {
        try {
            $request->validate([
                'item_id' => 'required|exists:carts,id',
                'backorder_quantity' => 'required|integer|min:1'
            ]);

            $item = \App\Models\Cart::findOrFail($request->item_id);

            // Verify the item belongs to an order assigned to this picker
            $order = Order::where('id', $item->order_id)
                         ->where('picker_id', Auth::user()->id)
                         ->firstOrFail();

            $backorder = $item->createBackorder($request->backorder_quantity);

            return response()->json([
                'status' => true,
                'message' => 'Backorder created successfully!',
                'backorder' => $backorder
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to create backorder: ' . $e->getMessage()
            ]);
        }
    }

    public function packDistributedItem(Request $request)
    {
        try {
            $request->validate([
                'item_id' => 'required|exists:carts,id',
                'distributions' => 'required|string'
            ]);

            $item = \App\Models\Cart::findOrFail($request->item_id);

            // Check if item is already packed to prevent duplicates
            if ($item->is_packed) {
                return response()->json([
                    'status' => false,
                    'message' => 'Item is already packed'
                ]);
            }

            // Verify the item belongs to an order assigned to this picker
            $order = Order::where('id', $item->order_id)
                         ->where('picker_id', Auth::user()->id)
                         ->firstOrFail();

            // Decode the distributions JSON
            $distributions = json_decode($request->distributions, true);
            if (!$distributions || !is_array($distributions)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid distributions data'
                ]);
            }

            $totalDistributed = array_sum($distributions);
            $availableQty = $item->getAvailableQuantityAttribute();

            // Validate total doesn't exceed available quantity
            if ($totalDistributed > $availableQty) {
                return response()->json([
                    'status' => false,
                    'message' => 'Total distributed quantity exceeds available stock'
                ]);
            }

            if ($totalDistributed == 0) {
                return response()->json([
                    'status' => false,
                    'message' => 'Please distribute items to boxes first'
                ]);
            }

            // Start database transaction to prevent partial updates
            \DB::beginTransaction();

            try {
                // Create cart entries for each box distribution
                foreach ($distributions as $boxId => $quantity) {
                    if ($quantity > 0) {
                        // Verify box exists and belongs to this order
                        $box = \App\Models\OrderBox::where('id', $boxId)
                                                  ->where('order_id', $order->id)
                                                  ->first();

                        if (!$box) {
                            throw new \Exception("Invalid box ID: {$boxId}");
                        }

                        // Create a new cart entry for this box
                        \App\Models\Cart::create([
                            'user_id' => $item->user_id,
                            'product_id' => $item->product_id,
                            'order_id' => $item->order_id,
                            'quantity' => $quantity,
                            'price' => $item->price,
                            'amount' => $quantity * $item->price,
                            'color' => $item->color,
                            'status' => 'new',
                            'box_id' => $boxId,
                            'is_packed' => true,
                            'packed_quantity' => $quantity,
                            'packed_at' => now(),
                            'original_cart_id' => $item->id
                        ]);
                    }
                }

                // Mark original item as packed and hide it from display
                $item->update([
                    'is_packed' => true,
                    'packed_quantity' => $totalDistributed,
                    'packed_at' => now(),
                    'status' => 'distributed' // Mark as distributed to hide from main list
                ]);

                // Create backorder for remaining quantity if any
                if ($totalDistributed < $item->quantity) {
                    $backorderQty = $item->quantity - $totalDistributed;
                    $item->createBackorder($backorderQty);
                }

                \DB::commit();

                return response()->json([
                    'status' => true,
                    'message' => "Item packed successfully across " . count($distributions) . " boxes!",
                    'packed_quantity' => $totalDistributed,
                    'backorder_quantity' => $item->quantity - $totalDistributed
                ]);

            } catch (\Exception $e) {
                \DB::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error packing distributed item: ' . $e->getMessage()
            ]);
        }
    }

    public function createSmartBackorder(Request $request)
    {
        try {
            $request->validate([
                'item_id' => 'required|exists:carts,id',
                'backorder_quantity' => 'required|integer|min:1'
            ]);

            $item = \App\Models\Cart::findOrFail($request->item_id);

            // Verify the item belongs to an order assigned to this picker
            $order = Order::where('id', $item->order_id)
                         ->where('picker_id', Auth::user()->id)
                         ->firstOrFail();

            $availableQty = $item->getAvailableQuantityAttribute();
            $backorderQty = $request->backorder_quantity;

            // Pack available quantity if any
            if ($availableQty > 0) {
                $item->update([
                    'packed_quantity' => $availableQty,
                    'is_packed' => true,
                    'packed_at' => now()
                ]);
            }

            // Create backorder for the shortage
            $backorder = $item->createBackorder($backorderQty);

            $message = $availableQty > 0
                ? "Packed {$availableQty} items and created backorder for {$backorderQty} items"
                : "Created backorder for {$backorderQty} items (no stock available)";

            return response()->json([
                'status' => true,
                'message' => $message,
                'backorder_created' => true,
                'packed_quantity' => $availableQty,
                'backorder_quantity' => $backorderQty
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error creating smart backorder: ' . $e->getMessage()
            ]);
        }
    }

    public function cleanupDuplicateItems(Request $request)
    {
        try {
            $request->validate([
                'order_id' => 'required|exists:orders,id'
            ]);

            $order = Order::where('id', $request->order_id)
                         ->where('picker_id', Auth::user()->id)
                         ->firstOrFail();

            // Find and remove duplicate cart items created by distributed packing
            $duplicates = \App\Models\Cart::where('order_id', $order->id)
                                         ->whereNotNull('original_cart_id')
                                         ->get();

            $cleanedCount = 0;
            foreach ($duplicates as $duplicate) {
                // Check if original item is already packed
                $original = \App\Models\Cart::find($duplicate->original_cart_id);
                if ($original && $original->is_packed) {
                    // Keep the distributed items, they're valid
                    continue;
                }

                // Remove invalid duplicates
                $duplicate->delete();
                $cleanedCount++;
            }

            return response()->json([
                'status' => true,
                'message' => "Cleaned up {$cleanedCount} duplicate items",
                'cleaned_count' => $cleanedCount
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error cleaning up duplicates: ' . $e->getMessage()
            ]);
        }
    }

    public function debugOrder($orderId)
    {
        try {
            $order = Order::with([
                'all_cart_items.product',
                'all_cart_items.color_name',
                'boxes.items.product'
            ])->where('id', $orderId)
              ->where('picker_id', Auth::user()->id)
              ->firstOrFail();

            $debug = [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'total_cart_items' => $order->all_cart_items->count(),
                'visible_cart_items' => $order->cart_info->count(),
                'boxes_count' => $order->boxes->count(),
                'cart_items' => $order->all_cart_items->map(function($item) {
                    return [
                        'id' => $item->id,
                        'product_id' => $item->product_id,
                        'product_title' => $item->product->title ?? 'No Product',
                        'quantity' => $item->quantity,
                        'packed_quantity' => $item->packed_quantity,
                        'status' => $item->status,
                        'is_packed' => $item->is_packed,
                        'box_id' => $item->box_id,
                        'original_cart_id' => $item->original_cart_id
                    ];
                }),
                'boxes' => $order->boxes->map(function($box) {
                    return [
                        'id' => $box->id,
                        'box_label' => $box->box_label,
                        'items_count' => $box->items->count(),
                        'items' => $box->items->map(function($item) {
                            return [
                                'id' => $item->id,
                                'product_title' => $item->product->title ?? 'No Product',
                                'quantity' => $item->quantity,
                                'packed_quantity' => $item->packed_quantity
                            ];
                        })
                    ];
                })
            ];

            return response()->json($debug, 200, [], JSON_PRETTY_PRINT);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Debug failed: ' . $e->getMessage()
            ]);
        }
    }

    public function cleanupBoxes(Request $request)
    {
        try {
            $request->validate([
                'order_id' => 'required|exists:orders,id'
            ]);

            $order = Order::where('id', $request->order_id)
                         ->where('picker_id', Auth::user()->id)
                         ->firstOrFail();

            // Get all boxes for this order
            $boxes = $order->boxes()->orderBy('box_number')->get();

            \Log::info('Cleaning up boxes for order', [
                'order_id' => $order->id,
                'boxes_before_cleanup' => $boxes->count(),
                'boxes' => $boxes->map(function($box) {
                    return [
                        'id' => $box->id,
                        'box_number' => $box->box_number,
                        'box_label' => $box->box_label,
                        'length' => $box->length,
                        'width' => $box->width,
                        'height' => $box->height,
                        'weight' => $box->weight
                    ];
                })
            ]);

            // Remove duplicate boxes (boxes with same box_number)
            $seenBoxNumbers = [];
            $duplicatesRemoved = 0;

            foreach ($boxes as $box) {
                if (in_array($box->box_number, $seenBoxNumbers)) {
                    // This is a duplicate
                    \Log::info('Removing duplicate box', [
                        'box_id' => $box->id,
                        'box_number' => $box->box_number
                    ]);
                    $box->delete();
                    $duplicatesRemoved++;
                } else {
                    $seenBoxNumbers[] = $box->box_number;
                }
            }

            // Renumber remaining boxes sequentially
            $remainingBoxes = $order->boxes()->orderBy('box_number')->get();
            $totalBoxes = $remainingBoxes->count();

            foreach ($remainingBoxes as $index => $box) {
                $newBoxNumber = $index + 1;
                $box->update([
                    'box_number' => $newBoxNumber,
                    'box_label' => "Box {$newBoxNumber} of {$totalBoxes}"
                ]);
            }

            return response()->json([
                'status' => true,
                'message' => "Cleanup completed. Removed {$duplicatesRemoved} duplicate boxes.",
                'duplicates_removed' => $duplicatesRemoved,
                'remaining_boxes' => $totalBoxes
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error cleaning up boxes: ' . $e->getMessage()
            ]);
        }
    }
}
