<?php

namespace App\Http\Controllers\Picker;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Support\Facades\Auth;

class OrderController extends Controller
{
    public function index(Request $request){
        $query = Order::where('picker_id', Auth::user()->id)
                     ->with(['cart_info', 'user']);

        // Apply status filter if provided
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        $orders = $query->latest()->paginate(10);

        // Preserve query parameters in pagination
        $orders->appends($request->query());

        return view('picker.orders.index', compact('orders'));
    }

    public function show($id)
    {
        $order = Order::with(['cart_info.product', 'cart_info.color_name', 'user'])
                     ->where('picker_id', Auth::user()->id)
                     ->findOrFail($id);
        return view('picker.orders.show', compact('order'));
    }

    public function startPacking(Request $request)
    {
        try {
            $order = Order::where('picker_id', Auth::user()->id)
                         ->where('id', $request->order_id)
                         ->where('status', 'sent_to_warehouse')
                         ->firstOrFail();

            $order->status = 'processing';
            $order->save();

            return response()->json([
                'status' => true,
                'message' => 'Order packing started successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to start packing: ' . $e->getMessage()
            ]);
        }
    }

    public function pack($id)
    {
        $order = Order::with(['cart_info.product', 'cart_info.color_name', 'cart_info.orderBox', 'boxes', 'user'])
                     ->where('picker_id', Auth::user()->id)
                     ->where('status', 'processing')
                     ->findOrFail($id);

        // Ensure at least one box exists
        if ($order->boxes()->count() == 0) {
            $this->createInitialBox($order);
        }

        return view('picker.orders.pack', compact('order'));
    }

    private function createInitialBox($order)
    {
        $box = \App\Models\OrderBox::create([
            'order_id' => $order->id,
            'box_number' => 1,
            'box_label' => 'Box 1 of 1',
            'max_weight' => 50.00
        ]);

        return $box;
    }

    public function packingList($id)
    {
        $order = Order::with(['cart_info.product', 'cart_info.color_name', 'user'])
                     ->where('picker_id', Auth::user()->id)
                     ->findOrFail($id);

        return view('picker.orders.packing-list', compact('order'));
    }

    public function updateItemStatus(Request $request)
    {
        try {
            $request->validate([
                'item_id' => 'required|exists:carts,id',
                'is_packed' => 'nullable|in:true,false,1,0',
                'is_returned' => 'nullable|in:true,false,1,0',
                'box_id' => 'nullable|exists:order_boxes,id',
                'packed_quantity' => 'nullable|integer|min:0',
                'picker_notes' => 'nullable|string'
            ]);

            $item = \App\Models\Cart::findOrFail($request->item_id);

            // Verify the item belongs to an order assigned to this picker
            $order = Order::where('id', $item->order_id)
                         ->where('picker_id', Auth::user()->id)
                         ->firstOrFail();

            // Handle quantity and backorder logic
            if ($request->has('packed_quantity')) {
                $packedQty = $request->packed_quantity;
                $originalQty = $item->quantity;

                if ($packedQty > $originalQty) {
                    return response()->json([
                        'status' => false,
                        'message' => 'Packed quantity cannot exceed ordered quantity'
                    ]);
                }

                // Check if we have enough stock
                if (!$item->canFulfillQuantity($packedQty)) {
                    $availableQty = $item->getAvailableQuantityAttribute();
                    $backorderQty = $originalQty - $availableQty;

                    if ($availableQty > 0) {
                        // Partial fulfillment
                        $item->packed_quantity = $availableQty;
                        $item->createBackorder($backorderQty);

                        return response()->json([
                            'status' => true,
                            'message' => "Partial fulfillment: {$availableQty} packed, {$backorderQty} backordered",
                            'backorder_created' => true,
                            'packed_quantity' => $availableQty,
                            'backorder_quantity' => $backorderQty
                        ]);
                    } else {
                        // Full backorder
                        $item->createBackorder($originalQty);

                        return response()->json([
                            'status' => true,
                            'message' => "Item fully backordered due to insufficient stock",
                            'backorder_created' => true,
                            'backorder_quantity' => $originalQty
                        ]);
                    }
                } else {
                    $item->packed_quantity = $packedQty;

                    // Create backorder for remaining quantity if any
                    if ($packedQty < $originalQty) {
                        $backorderQty = $originalQty - $packedQty;
                        $item->createBackorder($backorderQty);
                    }
                }
            }

            if ($request->has('is_packed')) {
                $isPacked = filter_var($request->is_packed, FILTER_VALIDATE_BOOLEAN);
                $item->is_packed = $isPacked;
                if ($isPacked) {
                    $item->packed_at = now();
                }
            }

            if ($request->has('is_returned')) {
                $isReturned = filter_var($request->is_returned, FILTER_VALIDATE_BOOLEAN);
                $item->is_returned_by_picker = $isReturned;
            }

            if ($request->has('box_id')) {
                $item->box_id = $request->box_id;

                // Update the old box field for backward compatibility
                if ($request->box_id) {
                    $box = \App\Models\OrderBox::find($request->box_id);
                    $item->box = $box ? $box->box_number : null;
                }
            }

            if ($request->has('picker_notes')) {
                $item->picker_notes = $request->picker_notes;
            }

            $item->save();

            return response()->json([
                'status' => true,
                'message' => 'Item status updated successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to update item: ' . $e->getMessage()
            ]);
        }
    }

    public function createBox(Request $request)
    {
        try {
            $request->validate([
                'order_id' => 'required|exists:orders,id',
                'length' => 'nullable|numeric|min:0',
                'width' => 'nullable|numeric|min:0',
                'height' => 'nullable|numeric|min:0',
                'max_weight' => 'nullable|numeric|min:0|max:200'
            ]);

            $order = Order::where('id', $request->order_id)
                         ->where('picker_id', Auth::user()->id)
                         ->firstOrFail();

            $nextBoxNumber = $order->boxes()->max('box_number') + 1;

            $box = \App\Models\OrderBox::create([
                'order_id' => $order->id,
                'box_number' => $nextBoxNumber,
                'length' => $request->length,
                'width' => $request->width,
                'height' => $request->height,
                'max_weight' => $request->max_weight ?? 50.00
            ]);

            // Update all box labels
            $this->updateBoxLabels($order);

            return response()->json([
                'status' => true,
                'message' => 'Box created successfully!',
                'box' => $box
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to create box: ' . $e->getMessage()
            ]);
        }
    }

    public function updateBox(Request $request, $boxId)
    {
        try {
            $request->validate([
                'length' => 'nullable|numeric|min:0',
                'width' => 'nullable|numeric|min:0',
                'height' => 'nullable|numeric|min:0',
                'weight' => 'nullable|numeric|min:0',
                'max_weight' => 'nullable|numeric|min:0|max:200',
                'is_full' => 'boolean',
                'notes' => 'nullable|string'
            ]);

            $box = \App\Models\OrderBox::findOrFail($boxId);

            // Verify the box belongs to an order assigned to this picker
            $order = Order::where('id', $box->order_id)
                         ->where('picker_id', Auth::user()->id)
                         ->firstOrFail();

            $box->update($request->only([
                'length', 'width', 'height', 'weight', 'max_weight', 'is_full', 'notes'
            ]));

            if ($request->has('is_full') && $request->is_full) {
                $box->markAsFull();
            }

            return response()->json([
                'status' => true,
                'message' => 'Box updated successfully!',
                'box' => $box->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to update box: ' . $e->getMessage()
            ]);
        }
    }

    private function updateBoxLabels($order)
    {
        $boxes = $order->boxes()->orderBy('box_number')->get();
        $totalBoxes = $boxes->count();

        foreach ($boxes as $box) {
            $box->updateBoxLabel($totalBoxes);
        }
    }

    public function printBoxLabel($boxId)
    {
        try {
            $box = \App\Models\OrderBox::with(['order.user', 'items.product', 'items.color_name'])
                                      ->findOrFail($boxId);

            // Verify the box belongs to an order assigned to this picker
            $order = Order::where('id', $box->order_id)
                         ->where('picker_id', Auth::user()->id)
                         ->firstOrFail();

            return view('picker.orders.box-label', compact('box', 'order'));
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to generate box label: ' . $e->getMessage()
            ]);
        }
    }

    public function completeOrder(Request $request, $orderId)
    {
        try {
            $order = Order::with(['cart_info', 'boxes'])
                         ->where('id', $orderId)
                         ->where('picker_id', Auth::user()->id)
                         ->where('status', 'processing')
                         ->firstOrFail();

            // Check if order can be completed
            if (!$order->canBeCompleted()) {
                return response()->json([
                    'status' => false,
                    'message' => 'Order cannot be completed. Some items are not packed or backordered.'
                ]);
            }

            // Complete the order
            $order->completeOrder();

            // Log the completion
            \App\Models\OrderHistory::create([
                'order_id' => $order->id,
                'user_id' => Auth::user()->id,
                'customer_id' => $order->user_id,
                'action' => 'completed_packing',
                'details' => [
                    'completed_by' => Auth::user()->first_name . ' ' . Auth::user()->last_name,
                    'total_boxes' => $order->boxes()->count(),
                    'packing_progress' => $order->getPackingProgressAttribute(),
                    'backorders_created' => $order->hasBackorders()
                ]
            ]);

            return response()->json([
                'status' => true,
                'message' => 'Order completed successfully!',
                'order_status' => $order->status,
                'total_boxes' => $order->total_boxes,
                'has_backorders' => $order->hasBackorders()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to complete order: ' . $e->getMessage()
            ]);
        }
    }

    public function getOrderProgress($orderId)
    {
        try {
            $order = Order::with(['cart_info', 'boxes'])
                         ->where('id', $orderId)
                         ->where('picker_id', Auth::user()->id)
                         ->firstOrFail();

            $totalItems = $order->cart_info->sum('quantity');
            $packedItems = $order->cart_info->sum('packed_quantity');
            $backorderedItems = $order->cart_info->sum('backorder_quantity');

            return response()->json([
                'status' => true,
                'progress' => [
                    'total_items' => $totalItems,
                    'packed_items' => $packedItems,
                    'backorder_items' => $backorderedItems,
                    'progress_percentage' => $order->getPackingProgressAttribute(),
                    'total_boxes' => $order->total_boxes,
                    'can_complete' => $order->canBeCompleted()
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to get order progress: ' . $e->getMessage()
            ]);
        }
    }

    public function createBackorder(Request $request)
    {
        try {
            $request->validate([
                'item_id' => 'required|exists:carts,id',
                'backorder_quantity' => 'required|integer|min:1'
            ]);

            $item = \App\Models\Cart::findOrFail($request->item_id);

            // Verify the item belongs to an order assigned to this picker
            $order = Order::where('id', $item->order_id)
                         ->where('picker_id', Auth::user()->id)
                         ->firstOrFail();

            $backorder = $item->createBackorder($request->backorder_quantity);

            return response()->json([
                'status' => true,
                'message' => 'Backorder created successfully!',
                'backorder' => $backorder
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to create backorder: ' . $e->getMessage()
            ]);
        }
    }

    public function packDistributedItem(Request $request)
    {
        try {
            $request->validate([
                'item_id' => 'required|exists:carts,id',
                'distributions' => 'required|array',
                'distributions.*' => 'integer|min:1'
            ]);

            $item = \App\Models\Cart::findOrFail($request->item_id);

            // Verify the item belongs to an order assigned to this picker
            $order = Order::where('id', $item->order_id)
                         ->where('picker_id', Auth::user()->id)
                         ->firstOrFail();

            $totalDistributed = array_sum($request->distributions);
            $availableQty = $item->getAvailableQuantityAttribute();

            // Validate total doesn't exceed available quantity
            if ($totalDistributed > $availableQty) {
                return response()->json([
                    'status' => false,
                    'message' => 'Total distributed quantity exceeds available stock'
                ]);
            }

            // Create cart entries for each box distribution
            foreach ($request->distributions as $boxId => $quantity) {
                if ($quantity > 0) {
                    // Create a new cart entry for this box
                    \App\Models\Cart::create([
                        'user_id' => $item->user_id,
                        'product_id' => $item->product_id,
                        'order_id' => $item->order_id,
                        'quantity' => $quantity,
                        'price' => $item->price,
                        'amount' => $quantity * $item->price,
                        'color' => $item->color,
                        'status' => 'new',
                        'box_id' => $boxId,
                        'is_packed' => true,
                        'packed_quantity' => $quantity,
                        'packed_at' => now(),
                        'original_cart_id' => $item->id
                    ]);
                }
            }

            // Mark original item as packed and update quantities
            $item->update([
                'is_packed' => true,
                'packed_quantity' => $totalDistributed,
                'packed_at' => now()
            ]);

            // Create backorder for remaining quantity if any
            if ($totalDistributed < $item->quantity) {
                $backorderQty = $item->quantity - $totalDistributed;
                $item->createBackorder($backorderQty);
            }

            return response()->json([
                'status' => true,
                'message' => "Item packed successfully across {count($request->distributions)} boxes!"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error packing distributed item: ' . $e->getMessage()
            ]);
        }
    }

    public function createSmartBackorder(Request $request)
    {
        try {
            $request->validate([
                'item_id' => 'required|exists:carts,id',
                'backorder_quantity' => 'required|integer|min:1'
            ]);

            $item = \App\Models\Cart::findOrFail($request->item_id);

            // Verify the item belongs to an order assigned to this picker
            $order = Order::where('id', $item->order_id)
                         ->where('picker_id', Auth::user()->id)
                         ->firstOrFail();

            $availableQty = $item->getAvailableQuantityAttribute();
            $backorderQty = $request->backorder_quantity;

            // Pack available quantity if any
            if ($availableQty > 0) {
                $item->update([
                    'packed_quantity' => $availableQty,
                    'is_packed' => true,
                    'packed_at' => now()
                ]);
            }

            // Create backorder for the shortage
            $backorder = $item->createBackorder($backorderQty);

            $message = $availableQty > 0
                ? "Packed {$availableQty} items and created backorder for {$backorderQty} items"
                : "Created backorder for {$backorderQty} items (no stock available)";

            return response()->json([
                'status' => true,
                'message' => $message,
                'backorder_created' => true,
                'packed_quantity' => $availableQty,
                'backorder_quantity' => $backorderQty
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error creating smart backorder: ' . $e->getMessage()
            ]);
        }
    }
}
