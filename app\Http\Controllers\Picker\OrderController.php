<?php

namespace App\Http\Controllers\Picker;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;

class OrderController extends Controller
{
     public function index(){
        $orders = Order::where('picker_id',Auth()->user()->id)->latest()->paginate(10);
        return view('picker.orders.index',compact('orders'));
    }

    public function show($id)
    {
        $order=Order::find($id);
        return view('salesman.orders.show')->with('order',$order);
    }
}
