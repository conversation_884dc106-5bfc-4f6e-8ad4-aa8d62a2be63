@extends('backend.layouts.master')

@section('title','Lamart || Order Detail')

@section('main-content')
<div class="card">
    <h5 class="card-header">Order Detail</h5>
    <div class="card-body">
        @if($order)
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Order Information</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Order Number:</strong></td>
                                <td>{{$order->order_number}}</td>
                            </tr>
                            <tr>
                                <td><strong>Order Date:</strong></td>
                                <td>{{$order->created_at->format('M d, Y h:i A')}}</td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    @if($order->status=='sent_to_warehouse')
                                        <span class="badge badge-warning">Sent to Warehouse</span>
                                    @elseif($order->status=='processing')
                                        <span class="badge badge-info">Being Packed</span>
                                    @elseif($order->status=='shipped')
                                        <span class="badge badge-success">Completed</span>
                                    @else
                                        <span class="badge badge-primary">{{ucfirst($order->status)}}</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Delivery Method:</strong></td>
                                <td>{{ucfirst($order->delivery_method ?? 'N/A')}}</td>
                            </tr>
                            <tr>
                                <td><strong>Total Amount:</strong></td>
                                <td>${{number_format($order->total_amount,2)}}</td>
                            </tr>
                            <tr>
                                <td><strong>Total Weight:</strong></td>
                                <td>{{$order->total_weight ?? 'N/A'}}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Customer Information</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Name:</strong></td>
                                <td>{{$order->first_name.' '.$order->last_name}}</td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>{{$order->email}}</td>
                            </tr>
                            <tr>
                                <td><strong>Phone:</strong></td>
                                <td>{{$order->phone}}</td>
                            </tr>
                            <tr>
                                <td><strong>Address:</strong></td>
                                <td>{{$order->address1}}<br>
                                    @if($order->address2){{$order->address2}}<br>@endif
                                    {{$order->country}}, {{$order->post_code}}
                                </td>
                            </tr>
                            @if($order->delivery_instructions)
                            <tr>
                                <td><strong>Delivery Instructions:</strong></td>
                                <td>{{$order->delivery_instructions}}</td>
                            </tr>
                            @endif
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">Order Items</h6>
                        <div>
                            @if($order->status == 'sent_to_warehouse')
                            <button class="btn btn-success btn-sm start-packing" data-id="{{$order->id}}">
                                <i class="fas fa-play"></i> Start Packing
                            </button>
                            @endif
                            
                            @if($order->status == 'processing')
                            <a href="{{route('picker.orders.pack', $order->id)}}" class="btn btn-warning btn-sm">
                                <i class="fas fa-box"></i> Continue Packing
                            </a>
                            @endif
                            
                            <button class="btn btn-info btn-sm" onclick="printPackingList()">
                                <i class="fas fa-print"></i> Print Packing List
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Item #</th>
                                        <th>Product</th>
                                        <th>Color</th>
                                        <th>Price</th>
                                        <th>Quantity</th>
                                        <th>Total</th>
                                        <th>Status</th>
                                        <th>Box</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($order->cart_info as $cart)
                                    <tr>
                                        <td>{{$cart->product->item_number ?? 'N/A'}}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($cart->product->photo)
                                                <img src="{{$cart->product->photo}}" class="img-fluid" style="max-width:50px" alt="{{$cart->product->photo}}">
                                                @endif
                                                <div class="ml-2">
                                                    <strong>{{$cart->product->title}}</strong><br>
                                                    <small class="text-muted">{{$cart->product->summary}}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{$cart->color_name->name ?? 'N/A'}}</td>
                                        <td>${{number_format($cart->price,2)}}</td>
                                        <td>{{$cart->quantity}}</td>
                                        <td>${{number_format($cart->amount,2)}}</td>
                                        <td>
                                            @if($cart->is_packed)
                                                <span class="badge badge-success">Packed</span>
                                            @elseif($cart->is_returned_by_picker)
                                                <span class="badge badge-danger">Returned</span>
                                            @else
                                                <span class="badge badge-warning">Pending</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge badge-secondary">Box {{$cart->box}}</span>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-12">
                <a href="{{route('picker.orders')}}" class="btn btn-primary">
                    <i class="fas fa-arrow-left"></i> Back to Orders
                </a>
            </div>
        </div>
        @endif
    </div>
</div>

<!-- Packing List Print Modal -->
<div class="modal fade" id="packingListModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Packing List - Order #{{$order->order_number}}</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="packingListContent">
                <!-- Packing list content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="window.print()">Print</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .table td, .table th {
        vertical-align: middle;
    }
    @media print {
        .modal-header, .modal-footer, .btn {
            display: none !important;
        }
    }
</style>
@endpush

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
<script>
    // Start packing functionality
    $('.start-packing').click(function(e) {
        e.preventDefault();
        var order_id = $(this).data('id');
        var token = $("meta[name='csrf-token']").attr("content");
        
        $.ajax({
            url: "{{route('picker.orders.start-packing')}}",
            type: 'POST',
            data: {
                "_token": token,
                "order_id": order_id
            },
            success: function(response) {
                if(response.status) {
                    swal({
                        title: "Success!",
                        text: response.message,
                        type: "success"
                    }, function() {
                        location.reload();
                    });
                } else {
                    swal("Error!", response.message, "error");
                }
            },
            error: function() {
                swal("Error!", "Something went wrong", "error");
            }
        });
    });

    function printPackingList() {
        // Load packing list content and show modal
        $.get("{{route('picker.orders.packing-list', $order->id)}}", function(data) {
            $('#packingListContent').html(data);
            $('#packingListModal').modal('show');
        });
    }
</script>
@endpush
