@extends('backend.layouts.master')
@section('title','Lamart || Warehouse Dashboard')
@section('main-content')
<div class="container-fluid">
    @include('backend.layouts.notification')
    
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
      <h1 class="h3 mb-0 text-gray-800">Warehouse Dashboard</h1>
      <div>
        <a href="{{route('orders.index')}}" class="btn btn-primary btn-sm">
          <i class="fas fa-clipboard-list"></i> Manage Orders
        </a>
        <a href="{{route('admin.backorders.index')}}" class="btn btn-warning btn-sm">
          <i class="fas fa-exclamation-triangle"></i> View Backorders
        </a>
      </div>
    </div>

    <!-- Content Row -->
    <div class="row">
      <!-- Total Orders -->
      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Orders</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$totalOrders}}</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pending Orders -->
      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending Assignment</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$pendingOrders}}</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-clock fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Warehouse Orders -->
      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">In Warehouse</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$warehouseOrders}}</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-warehouse fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Processing Orders -->
      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Being Packed</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$processingOrders}}</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-box fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Second Row -->
    <div class="row">
      <!-- Active Pickers -->
      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-dark shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-dark text-uppercase mb-1">Active Pickers</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$activePickers}} / {{$totalPickers}}</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-users fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Backorders -->
      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Backorders</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$totalBackorders}}</div>
                <div class="text-xs text-muted">${{number_format($backorderValue, 2)}} value</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Shipped Today -->
      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Shipped Orders</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$shippedOrders}}</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-shipping-fast fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Content Row -->
    <div class="row">
      <!-- Recent Orders -->
      <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
          <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Recent Warehouse Activity</h6>
            <a href="{{route('orders.index')}}" class="btn btn-primary btn-sm">View All</a>
          </div>
          <div class="card-body">
            @if($recentOrders->count() > 0)
            <div class="table-responsive">
              <table class="table table-bordered">
                <thead>
                  <tr>
                    <th>Order #</th>
                    <th>Customer</th>
                    <th>Picker</th>
                    <th>Status</th>
                    <th>Total</th>
                    <th>Date</th>
                  </tr>
                </thead>
                <tbody>
                  @foreach($recentOrders as $order)
                  <tr>
                    <td><strong>{{$order->order_number}}</strong></td>
                    <td>{{$order->first_name}} {{$order->last_name}}</td>
                    <td>
                      @if($order->picker)
                        <span class="badge badge-info">{{$order->picker->first_name}} {{$order->picker->last_name}}</span>
                      @else
                        <span class="text-muted">Unassigned</span>
                      @endif
                    </td>
                    <td>
                      @if($order->status == 'sent_to_warehouse')
                        <span class="badge badge-warning">In Warehouse</span>
                      @elseif($order->status == 'processing')
                        <span class="badge badge-info">Being Packed</span>
                      @else
                        <span class="badge badge-secondary">{{ucfirst($order->status)}}</span>
                      @endif
                    </td>
                    <td>${{number_format($order->total_amount, 2)}}</td>
                    <td>{{$order->created_at->format('M d, H:i')}}</td>
                  </tr>
                  @endforeach
                </tbody>
              </table>
            </div>
            @else
            <div class="text-center py-4">
              <i class="fas fa-inbox fa-3x text-gray-300 mb-3"></i>
              <h5 class="text-gray-500">No recent warehouse activity</h5>
            </div>
            @endif
          </div>
        </div>
      </div>

      <!-- Picker Performance -->
      <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
          <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Picker Performance</h6>
          </div>
          <div class="card-body">
            @if($pickerPerformance->count() > 0)
            @foreach($pickerPerformance as $picker)
            <div class="mb-3">
              <div class="d-flex justify-content-between">
                <span class="font-weight-bold">{{$picker->first_name}} {{$picker->last_name}}</span>
                <span class="text-muted">{{$picker->completed_orders}}/{{$picker->total_orders}}</span>
              </div>
              <div class="progress" style="height: 8px;">
                <div class="progress-bar bg-success" role="progressbar" 
                     style="width: {{$picker->total_orders > 0 ? ($picker->completed_orders / $picker->total_orders) * 100 : 0}}%">
                </div>
              </div>
              <small class="text-muted">
                Processing: {{$picker->processing_orders}} | 
                Completed: {{$picker->completed_orders}}
              </small>
            </div>
            @endforeach
            @else
            <div class="text-center py-4">
              <i class="fas fa-user-slash fa-2x text-gray-300 mb-2"></i>
              <p class="text-gray-500">No active pickers</p>
            </div>
            @endif
          </div>
        </div>

        <!-- Recent Backorders -->
        <div class="card shadow mb-4">
          <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-danger">Recent Backorders</h6>
          </div>
          <div class="card-body">
            @if($recentBackorders->count() > 0)
            @foreach($recentBackorders as $backorder)
            <div class="d-flex justify-content-between align-items-center mb-2">
              <div>
                <strong>{{$backorder->product->title}}</strong><br>
                <small class="text-muted">Order #{{$backorder->originalOrder->order_number}}</small>
              </div>
              <div class="text-right">
                <span class="badge badge-warning">{{$backorder->quantity}}</span><br>
                <small class="text-muted">${{number_format($backorder->total_amount, 2)}}</small>
              </div>
            </div>
            @endforeach
            <div class="text-center mt-3">
              <a href="{{route('admin.backorders.index')}}" class="btn btn-warning btn-sm">
                View All Backorders
              </a>
            </div>
            @else
            <div class="text-center py-4">
              <i class="fas fa-check-circle fa-2x text-gray-300 mb-2"></i>
              <p class="text-gray-500">No pending backorders</p>
            </div>
            @endif
          </div>
        </div>
      </div>
    </div>

</div>
@endsection
