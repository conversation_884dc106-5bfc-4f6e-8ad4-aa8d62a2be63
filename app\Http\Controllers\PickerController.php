<?php

namespace App\Http\Controllers;
use App\Models\Order;
use Illuminate\Http\Request;

class PickerController extends Controller
{
    public function index()
    {
        $orders = Order::where('picker_id',Auth()->user()->id)->count();
        $latest_orders = Order::where('picker_id',Auth()->user()->id)->latest()->limit(5)->get();
        return view('picker.dashboard',compact('orders','latest_orders'));
    }
}
