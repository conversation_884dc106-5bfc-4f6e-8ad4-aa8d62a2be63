@extends('backend.layouts.master')

@section('title','Lamart || My Backorders')

@section('main-content')
<div class="container-fluid">
    @include('backend.layouts.notification')
    
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
      <h1 class="h3 mb-0 text-gray-800">My Backorders</h1>
      <a href="{{route('picker.orders')}}" class="btn btn-primary btn-sm">
        <i class="fas fa-arrow-left"></i> Back to Orders
      </a>
    </div>

    <!-- Content Row -->
    <div class="row">
      <!-- Total Backorders -->
      <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending Backorders</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$totalBackorders}}</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Fulfilled Backorders -->
      <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Fulfilled</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$fulfilledBackorders}}</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Backorder Value -->
      <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Value</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">${{number_format($backorderValue, 2)}}</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Backorders Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
          <h6 class="m-0 font-weight-bold text-primary">Backorder History</h6>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            @if(count($backorders)>0)
            <table class="table table-bordered" id="backorder-dataTable" width="100%" cellspacing="0">
              <thead>
                <tr>
                  <th>Original Order</th>
                  <th>Product</th>
                  <th>Color</th>
                  <th>Quantity</th>
                  <th>Price</th>
                  <th>Total</th>
                  <th>Status</th>
                  <th>Created</th>
                  <th>New Order</th>
                </tr>
              </thead>
              <tbody>
                @foreach($backorders as $backorder)
                    <tr>
                        <td><strong>{{$backorder->originalOrder->order_number}}</strong></td>
                        <td>
                          <div class="d-flex align-items-center">
                            @if($backorder->product->photo)
                            <img src="{{$backorder->product->photo}}" class="img-fluid mr-2" style="max-width:40px" alt="Product">
                            @endif
                            <div>
                              <strong>{{$backorder->product->title}}</strong><br>
                              <small class="text-muted">{{$backorder->product->summary}}</small>
                            </div>
                          </div>
                        </td>
                        <td>{{$backorder->color->name ?? 'N/A'}}</td>
                        <td><span class="badge badge-warning">{{$backorder->quantity}}</span></td>
                        <td>${{number_format($backorder->price,2)}}</td>
                        <td>${{number_format($backorder->total_amount,2)}}</td>
                        <td>
                            @if($backorder->status=='pending')
                                <span class="badge badge-warning">Pending</span>
                            @elseif($backorder->status=='fulfilled')
                                <span class="badge badge-success">Fulfilled</span>
                            @elseif($backorder->status=='cancelled')
                                <span class="badge badge-danger">Cancelled</span>
                            @elseif($backorder->status=='combined')
                                <span class="badge badge-info">Combined</span>
                            @endif
                        </td>
                        <td>{{$backorder->created_at->format('M d, Y')}}</td>
                        <td>
                            @if($backorder->newOrder)
                                <a href="{{route('picker.orders.show', $backorder->newOrder->id)}}" class="btn btn-sm btn-primary">
                                    {{$backorder->newOrder->order_number}}
                                </a>
                            @else
                                <span class="text-muted">-</span>
                            @endif
                        </td>
                    </tr>
                @endforeach
              </tbody>
            </table>
            <span style="float:right">{{$backorders->links()}}</span>
            @else
              <div class="text-center py-5">
                <i class="fas fa-check-circle fa-3x text-gray-300 mb-3"></i>
                <h5 class="text-gray-500">No backorders found</h5>
                <p class="text-gray-400">Great job! You haven't created any backorders yet.</p>
              </div>
            @endif
          </div>
        </div>
    </div>

    <!-- Information Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
          <h6 class="m-0 font-weight-bold text-info">
            <i class="fas fa-info-circle"></i> About Backorders
          </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="font-weight-bold text-primary">What are Backorders?</h6>
                    <p class="text-muted">
                        Backorders are created automatically when there isn't enough inventory to fulfill an order completely. 
                        This ensures customers still receive the items that are available while the remaining items are tracked for future fulfillment.
                    </p>
                    
                    <h6 class="font-weight-bold text-primary">When are Backorders Created?</h6>
                    <ul class="text-muted">
                        <li>When you try to pack more items than available in stock</li>
                        <li>When inventory runs out during the packing process</li>
                        <li>When you manually create a backorder for insufficient stock</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="font-weight-bold text-primary">Backorder Status Meanings:</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <span class="badge badge-warning">Pending</span> 
                            <span class="text-muted ml-2">Waiting for office to create new order</span>
                        </li>
                        <li class="mb-2">
                            <span class="badge badge-success">Fulfilled</span> 
                            <span class="text-muted ml-2">New order created and ready for processing</span>
                        </li>
                        <li class="mb-2">
                            <span class="badge badge-info">Combined</span> 
                            <span class="text-muted ml-2">Added to an existing customer order</span>
                        </li>
                        <li class="mb-2">
                            <span class="badge badge-danger">Cancelled</span> 
                            <span class="text-muted ml-2">Backorder was cancelled by office</span>
                        </li>
                    </ul>
                    
                    <div class="alert alert-info mt-3">
                        <strong>Note:</strong> The office team manages backorder fulfillment. You'll be notified when backorders are converted to new orders for processing.
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
@endsection

@push('styles')
  <link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
  <style>
      div.dataTables_wrapper div.dataTables_paginate{
          display: none;
      }
      .badge {
          font-size: 0.8em;
      }
  </style>
@endpush

@push('scripts')
  <!-- Page level plugins -->
  <script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
  <script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>

  <!-- Page level custom scripts -->
  <script src="{{asset('backend/js/demo/datatables-demo.js')}}"></script>
  <script>
      $('#backorder-dataTable').DataTable( {
            "columnDefs":[
                {
                    "orderable":false,
                    "targets":[8] // New Order column
                }
            ]
        } );
  </script>
@endpush
