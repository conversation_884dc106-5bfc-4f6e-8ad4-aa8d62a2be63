<?php

namespace App\Http\Controllers\Picker;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Backorder;
use App\Models\Order;
use Illuminate\Support\Facades\Auth;

class BackorderController extends Controller
{
    public function index()
    {
        // Get backorders for orders that were assigned to this picker
        $backorders = Backorder::with(['originalOrder', 'product', 'color', 'newOrder'])
                              ->whereHas('originalOrder', function($query) {
                                  $query->where('picker_id', Auth::user()->id);
                              })
                              ->latest()
                              ->paginate(20);
        
        // Get statistics
        $totalBackorders = Backorder::whereHas('originalOrder', function($query) {
                                      $query->where('picker_id', Auth::user()->id);
                                  })
                                  ->where('status', 'pending')
                                  ->count();
        
        $fulfilledBackorders = Backorder::whereHas('originalOrder', function($query) {
                                         $query->where('picker_id', Auth::user()->id);
                                     })
                                     ->where('status', 'fulfilled')
                                     ->count();
        
        $backorderValue = Backorder::whereHas('originalOrder', function($query) {
                                    $query->where('picker_id', Auth::user()->id);
                                })
                                ->where('status', 'pending')
                                ->sum('total_amount');

        return view('picker.backorders.index', compact(
            'backorders', 
            'totalBackorders', 
            'fulfilledBackorders', 
            'backorderValue'
        ));
    }
}
