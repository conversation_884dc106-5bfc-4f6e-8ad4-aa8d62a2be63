<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Cart extends Model
{
    protected $fillable=['user_id','product_id','order_id','quantity','amount','price','status','is_packed','is_returned_by_picker','box','color'];

    protected $casts = [
        'is_packed' => 'boolean',
        'is_returned_by_picker' => 'boolean',
    ];

    // public function product(){
    //     return $this->hasOne('App\Models\Product','id','product_id');
    // }
    // public static function getAllProductFromCart(){
    //     return Cart::with('product')->where('user_id',auth()->user()->id)->get();
    // }
    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id')->with('item_colors');
    }

    public function order(){
        return $this->belongsTo(Order::class,'order_id');
    }

    public function color_name()
    {
        return $this->belongsTo(Color::class, 'color');
    }
}
