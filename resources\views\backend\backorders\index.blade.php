@extends('backend.layouts.master')

@section('title','Lamart || Backorders')

@section('main-content')
 <!-- DataTales Example -->
 <div class="card shadow mb-4">
     <div class="row">
         <div class="col-md-12">
            @include('backend.layouts.notification')
         </div>
     </div>
    <div class="card-header py-3">
      <h6 class="m-0 font-weight-bold text-primary float-left">Backorder Management</h6>
      <div class="float-right">
        <button class="btn btn-success btn-sm mr-2" id="bulkFulfillBtn" style="display: none;">
          <i class="fas fa-check"></i> Fulfill Selected
        </button>
        <button class="btn btn-info btn-sm" id="createBackorderOrderBtn">
          <i class="fas fa-plus"></i> Create Backorder Order
        </button>
      </div>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        @if(count($backorders)>0)
        <table class="table table-bordered" id="backorder-dataTable" width="100%" cellspacing="0">
          <thead>
            <tr>
              <th><input type="checkbox" id="selectAll"></th>
              <th>Original Order</th>
              <th>Customer</th>
              <th>Product</th>
              <th>Color</th>
              <th>Quantity</th>
              <th>Price</th>
              <th>Total</th>
              <th>Status</th>
              <th>Created</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            @foreach($backorders as $backorder)
                <tr>
                    <td>
                      <input type="checkbox" class="backorder-checkbox" value="{{$backorder->id}}" 
                             {{$backorder->status != 'pending' ? 'disabled' : ''}}>
                    </td>
                    <td><strong>{{$backorder->originalOrder->order_number}}</strong></td>
                    <td>{{$backorder->originalOrder->first_name}} {{$backorder->originalOrder->last_name}}</td>
                    <td>
                      <div class="d-flex align-items-center">
                        @if($backorder->product->photo)
                        <img src="{{$backorder->product->photo}}" class="img-fluid mr-2" style="max-width:40px" alt="Product">
                        @endif
                        <div>
                          <strong>{{$backorder->product->title}}</strong><br>
                          <small class="text-muted">{{$backorder->product->item_number}}</small>
                        </div>
                      </div>
                    </td>
                    <td>{{$backorder->color->name ?? 'N/A'}}</td>
                    <td><span class="badge badge-warning">{{$backorder->quantity}}</span></td>
                    <td>${{number_format($backorder->price,2)}}</td>
                    <td>${{number_format($backorder->total_amount,2)}}</td>
                    <td>
                        @if($backorder->status=='pending')
                            <span class="badge badge-warning">Pending</span>
                        @elseif($backorder->status=='fulfilled')
                            <span class="badge badge-success">Fulfilled</span>
                        @elseif($backorder->status=='cancelled')
                            <span class="badge badge-danger">Cancelled</span>
                        @elseif($backorder->status=='combined')
                            <span class="badge badge-info">Combined</span>
                        @endif
                    </td>
                    <td>{{$backorder->created_at->format('M d, Y')}}</td>
                    <td>
                        @if($backorder->status == 'pending')
                        <div class="btn-group" role="group">
                          <button class="btn btn-success btn-sm fulfill-backorder" data-id="{{$backorder->id}}" 
                                  data-toggle="tooltip" title="Fulfill" data-placement="bottom">
                            <i class="fas fa-check"></i>
                          </button>
                          <button class="btn btn-warning btn-sm combine-backorder" data-id="{{$backorder->id}}" 
                                  data-customer-id="{{$backorder->originalOrder->user_id}}"
                                  data-toggle="tooltip" title="Combine with Order" data-placement="bottom">
                            <i class="fas fa-link"></i>
                          </button>
                          <button class="btn btn-danger btn-sm cancel-backorder" data-id="{{$backorder->id}}" 
                                  data-toggle="tooltip" title="Cancel" data-placement="bottom">
                            <i class="fas fa-times"></i>
                          </button>
                        </div>
                        @else
                        <span class="text-muted">No actions</span>
                        @endif
                    </td>
                </tr>
            @endforeach
          </tbody>
        </table>
        <span style="float:right">{{$backorders->links()}}</span>
        @else
          <h6 class="text-center">No backorders found!</h6>
        @endif
      </div>
    </div>
</div>

<!-- Fulfill Backorder Modal -->
<div class="modal fade" id="fulfillBackorderModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Fulfill Backorder</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="fulfillBackorderForm">
                    <input type="hidden" id="fulfillBackorderId" name="backorder_id">
                    <div class="form-group">
                        <label>Create new order for this backorder?</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="fulfill_type" id="createNewOrder" value="new_order" checked>
                            <label class="form-check-label" for="createNewOrder">
                                Create new order
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="fulfill_type" id="markFulfilled" value="mark_fulfilled">
                            <label class="form-check-label" for="markFulfilled">
                                Mark as fulfilled (no new order)
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="fulfillNotes">Notes (optional):</label>
                        <textarea class="form-control" id="fulfillNotes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirmFulfillBackorder">Fulfill Backorder</button>
            </div>
        </div>
    </div>
</div>

<!-- Combine Backorder Modal -->
<div class="modal fade" id="combineBackorderModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Combine Backorder with Order</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="combineBackorderForm">
                    <input type="hidden" id="combineBackorderId" name="backorder_id">
                    <input type="hidden" id="combineCustomerId" name="customer_id">
                    <div class="form-group">
                        <label for="combineOrderId">Select existing order to combine with:</label>
                        <select class="form-control" id="combineOrderId" name="order_id" required>
                            <option value="">-- Select Order --</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="combineNotes">Notes (optional):</label>
                        <textarea class="form-control" id="combineNotes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" id="confirmCombineBackorder">Combine with Order</button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
  <link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" />
  <style>
      div.dataTables_wrapper div.dataTables_paginate{
          display: none;
      }
      .badge {
          font-size: 0.8em;
      }
  </style>
@endpush

@push('scripts')
  <!-- Page level plugins -->
  <script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
  <script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>

  <!-- Page level custom scripts -->
  <script src="{{asset('backend/js/demo/datatables-demo.js')}}"></script>
  <script>
      
      $('#backorder-dataTable').DataTable( {
            "columnDefs":[
                {
                    "orderable":false,
                    "targets":[0, 10] // Checkbox and Action columns
                }
            ]
        } );

        // Checkbox functionality
        $('#selectAll').change(function() {
            $('.backorder-checkbox:not(:disabled)').prop('checked', this.checked);
            toggleBulkFulfillButton();
        });

        $('.backorder-checkbox').change(function() {
            toggleBulkFulfillButton();
        });

        function toggleBulkFulfillButton() {
            const checkedBoxes = $('.backorder-checkbox:checked').length;
            if (checkedBoxes > 0) {
                $('#bulkFulfillBtn').show();
            } else {
                $('#bulkFulfillBtn').hide();
            }
        }

        // Individual backorder actions
        $('.fulfill-backorder').click(function() {
            const backorderId = $(this).data('id');
            $('#fulfillBackorderId').val(backorderId);
            $('#fulfillBackorderModal').modal('show');
        });

        $('.combine-backorder').click(function() {
            const backorderId = $(this).data('id');
            const customerId = $(this).data('customer-id');
            
            $('#combineBackorderId').val(backorderId);
            $('#combineCustomerId').val(customerId);
            
            // Load customer orders
            loadCustomerOrders(customerId);
            $('#combineBackorderModal').modal('show');
        });

        $('.cancel-backorder').click(function() {
            const backorderId = $(this).data('id');
            
            swal({
                title: "Cancel Backorder?",
                text: "This will permanently cancel this backorder. This action cannot be undone.",
                type: "warning",
                showCancelButton: true,
                confirmButtonText: "Yes, cancel it!"
            }, function() {
                cancelBackorder(backorderId);
            });
        });

        function loadCustomerOrders(customerId) {
            $.get(`/admin/customers/${customerId}/orders`, function(orders) {
                const select = $('#combineOrderId');
                select.empty().append('<option value="">-- Select Order --</option>');
                
                orders.forEach(order => {
                    if (order.status === 'pending' || order.status === 'processing') {
                        select.append(`<option value="${order.id}">${order.order_number} - $${order.total_amount}</option>`);
                    }
                });
            });
        }

        function cancelBackorder(backorderId) {
            $.ajax({
                url: `/admin/backorders/${backorderId}/cancel`,
                type: 'POST',
                data: {
                    "_token": $("meta[name='csrf-token']").attr("content")
                },
                success: function(response) {
                    if (response.status) {
                        swal("Success!", response.message, "success");
                        location.reload();
                    } else {
                        swal("Error!", response.message, "error");
                    }
                },
                error: function() {
                    swal("Error!", "Something went wrong", "error");
                }
            });
        }

  </script>
@endpush
