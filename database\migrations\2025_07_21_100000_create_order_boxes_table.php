<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_boxes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->integer('box_number');
            $table->string('box_label')->nullable(); // e.g., "Box 1 of 3"
            $table->decimal('length', 8, 2)->nullable(); // in inches
            $table->decimal('width', 8, 2)->nullable(); // in inches
            $table->decimal('height', 8, 2)->nullable(); // in inches
            $table->decimal('weight', 8, 2)->nullable(); // in lbs
            $table->decimal('max_weight', 8, 2)->default(50.00); // max weight limit
            $table->boolean('is_full')->default(false);
            $table->boolean('is_sealed')->default(false);
            $table->text('notes')->nullable();
            $table->json('shipping_info')->nullable(); // carrier, tracking, etc.
            $table->timestamp('packed_at')->nullable();
            $table->timestamp('sealed_at')->nullable();
            $table->timestamps();

            $table->unique(['order_id', 'box_number']);
            $table->index(['order_id', 'is_full']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_boxes');
    }
};
