<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->integer('picker_id')->nullable()->default(null);
            $table->string('total_weight')->nullable()->default(null);
            DB::statement("ALTER TABLE `orders` CHANGE `status` `status` ENUM('draft','pending','processing','shipped','delivered','cancelled','returned','sent_to_warehouse') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending';");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('picker_id');
            $table->dropColumn('total_weight');
            DB::statement("ALTER TABLE `orders` CHANGE `status` `status` ENUM('draft','pending','processing','shipped','delivered','cancelled','returned','sent_to_warehouse') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending';");
        });
    }
};
