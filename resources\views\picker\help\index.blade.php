@extends('backend.layouts.master')

@section('title','Lamart || Help & Guide')

@section('main-content')
<div class="container-fluid">
    @include('backend.layouts.notification')
    
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
      <h1 class="h3 mb-0 text-gray-800">Help & User Guide</h1>
      <a href="{{route('picker')}}" class="btn btn-primary btn-sm">
        <i class="fas fa-arrow-left"></i> Back to Dashboard
      </a>
    </div>

    <div class="row">
        <!-- Quick Start Guide -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-rocket"></i> Quick Start Guide
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item mb-3">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="font-weight-bold">1. Check Your Dashboard</h6>
                                <p class="text-muted">Start your day by checking the dashboard for new orders assigned to you.</p>
                            </div>
                        </div>
                        <div class="timeline-item mb-3">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="font-weight-bold">2. Start Packing</h6>
                                <p class="text-muted">Click "Start" on pending orders to begin the packing process.</p>
                            </div>
                        </div>
                        <div class="timeline-item mb-3">
                            <div class="timeline-marker bg-warning"></div>
                            <div class="timeline-content">
                                <h6 class="font-weight-bold">3. Manage Boxes</h6>
                                <p class="text-muted">Create boxes, assign items, and track dimensions and weight.</p>
                            </div>
                        </div>
                        <div class="timeline-item mb-3">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="font-weight-bold">4. Complete Order</h6>
                                <p class="text-muted">Print labels and mark the order as complete when all items are packed.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Status Guide -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i> Order Status Guide
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <span class="badge badge-warning badge-lg">Sent to Warehouse</span>
                        <p class="text-muted mt-2">Order has been assigned to you and is ready to start packing.</p>
                    </div>
                    <div class="mb-3">
                        <span class="badge badge-info badge-lg">Processing</span>
                        <p class="text-muted mt-2">You have started packing this order. Continue until all items are processed.</p>
                    </div>
                    <div class="mb-3">
                        <span class="badge badge-success badge-lg">Shipped</span>
                        <p class="text-muted mt-2">Order is complete and has been shipped to the customer.</p>
                    </div>
                    <div class="mb-3">
                        <span class="badge badge-danger badge-lg">Returned</span>
                        <p class="text-muted mt-2">Order was returned to office due to issues or customer request.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Packing Process -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-success">
                <i class="fas fa-box"></i> Packing Process Detailed Guide
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="font-weight-bold text-primary">Box Management</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-plus-circle text-success"></i>
                            <strong>Add Box:</strong> Click "Add Box" to create a new shipping box
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-ruler text-info"></i>
                            <strong>Dimensions:</strong> Enter Length × Width × Height in inches
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-weight text-warning"></i>
                            <strong>Weight:</strong> Track current weight vs. maximum capacity
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success"></i>
                            <strong>Mark Full:</strong> Indicate when a box is at capacity
                        </li>
                    </ul>

                    <h6 class="font-weight-bold text-primary mt-4">Item Assignment</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-mouse-pointer text-info"></i>
                            <strong>Select Box:</strong> Choose which box to assign items to
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-sort-numeric-up text-warning"></i>
                            <strong>Pack Quantity:</strong> Adjust quantity being packed
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-exclamation-triangle text-danger"></i>
                            <strong>Low Stock:</strong> System alerts when insufficient inventory
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="font-weight-bold text-primary">Backorder Handling</h6>
                    <div class="alert alert-warning">
                        <strong>What are Backorders?</strong><br>
                        When there isn't enough inventory to fulfill an order completely, the system creates a backorder for the missing items.
                    </div>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-exclamation-triangle text-warning"></i>
                            <strong>Automatic Creation:</strong> System creates backorders when stock is insufficient
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-hand-pointer text-info"></i>
                            <strong>Manual Creation:</strong> Click "Backorder" button for problem items
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-clipboard-list text-success"></i>
                            <strong>Track Progress:</strong> View your backorders in the Backorders section
                        </li>
                    </ul>

                    <h6 class="font-weight-bold text-primary mt-4">Label Printing</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-print text-info"></i>
                            <strong>Individual Labels:</strong> Print labels for specific boxes
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-print text-success"></i>
                            <strong>Batch Printing:</strong> Print all box labels at once
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-tags text-warning"></i>
                            <strong>Box Numbering:</strong> Labels show "Box 1 of 3", "Box 2 of 3", etc.
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Troubleshooting -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="fas fa-exclamation-triangle"></i> Common Issues & Solutions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="accordion" id="troubleshootingAccordion">
                        <div class="card">
                            <div class="card-header" id="issue1">
                                <h6 class="mb-0">
                                    <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapse1">
                                        Can't find enough inventory for an item
                                    </button>
                                </h6>
                            </div>
                            <div id="collapse1" class="collapse" data-parent="#troubleshootingAccordion">
                                <div class="card-body">
                                    <p>1. Check the available quantity shown in the packing interface</p>
                                    <p>2. Pack only the available quantity</p>
                                    <p>3. Click "Backorder" to create a backorder for the remaining items</p>
                                    <p>4. The office will handle restocking and fulfilling the backorder</p>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-header" id="issue2">
                                <h6 class="mb-0">
                                    <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapse2">
                                        Box is too heavy or too full
                                    </button>
                                </h6>
                            </div>
                            <div id="collapse2" class="collapse" data-parent="#troubleshootingAccordion">
                                <div class="card-body">
                                    <p>1. Click "Add Box" to create a new shipping box</p>
                                    <p>2. Move some items to the new box</p>
                                    <p>3. Update box dimensions and weight limits</p>
                                    <p>4. Mark the full box as complete</p>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-header" id="issue3">
                                <h6 class="mb-0">
                                    <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapse3">
                                        Can't complete an order
                                    </button>
                                </h6>
                            </div>
                            <div id="collapse3" class="collapse" data-parent="#troubleshootingAccordion">
                                <div class="card-body">
                                    <p>1. Ensure all items are either packed or backordered</p>
                                    <p>2. Check that all boxes have items assigned</p>
                                    <p>3. Verify box dimensions and weights are entered</p>
                                    <p>4. If issues persist, contact the office team</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-lightbulb"></i> Best Practices
                    </h6>
                </div>
                <div class="card-body">
                    <h6 class="font-weight-bold text-primary">Efficiency Tips</h6>
                    <ul class="text-muted">
                        <li>Start with the oldest orders first</li>
                        <li>Group similar items together in boxes</li>
                        <li>Check inventory before starting to pack</li>
                        <li>Use appropriate box sizes to minimize shipping costs</li>
                        <li>Double-check customer addresses before printing labels</li>
                    </ul>

                    <h6 class="font-weight-bold text-primary mt-4">Quality Control</h6>
                    <ul class="text-muted">
                        <li>Verify item quantities match the order</li>
                        <li>Check for damaged items before packing</li>
                        <li>Ensure fragile items are properly protected</li>
                        <li>Confirm box weight doesn't exceed limits</li>
                        <li>Print clear, readable labels</li>
                    </ul>

                    <h6 class="font-weight-bold text-primary mt-4">Communication</h6>
                    <ul class="text-muted">
                        <li>Report inventory issues immediately</li>
                        <li>Note any customer special requests</li>
                        <li>Communicate delays to the office team</li>
                        <li>Ask questions when unsure about procedures</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Information -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-info">
                <i class="fas fa-phone"></i> Need More Help?
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="text-center">
                        <i class="fas fa-headset fa-3x text-primary mb-3"></i>
                        <h6 class="font-weight-bold">Office Support</h6>
                        <p class="text-muted">For order questions and inventory issues</p>
                        <p><strong>Ext: 101</strong></p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <i class="fas fa-tools fa-3x text-warning mb-3"></i>
                        <h6 class="font-weight-bold">Technical Support</h6>
                        <p class="text-muted">For system issues and login problems</p>
                        <p><strong>Ext: 102</strong></p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <i class="fas fa-user-tie fa-3x text-success mb-3"></i>
                        <h6 class="font-weight-bold">Supervisor</h6>
                        <p class="text-muted">For urgent issues and escalations</p>
                        <p><strong>Ext: 100</strong></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}

.badge-lg {
    font-size: 0.9em;
    padding: 0.5rem 0.75rem;
}
</style>
@endsection
