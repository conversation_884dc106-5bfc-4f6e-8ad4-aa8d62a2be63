<?php

namespace App\Http\Controllers\Picker;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\Backorder;
use App\Models\Cart;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class ReportController extends Controller
{
    public function daily(Request $request)
    {
        $date = $request->input('date', Carbon::today()->format('Y-m-d'));
        $selectedDate = Carbon::parse($date);
        
        $pickerId = Auth::user()->id;
        
        // Orders processed today
        $ordersToday = Order::where('picker_id', $pickerId)
                           ->whereDate('updated_at', $selectedDate)
                           ->get();
        
        // Orders completed today
        $completedToday = Order::where('picker_id', $pickerId)
                              ->where('status', 'shipped')
                              ->whereDate('updated_at', $selectedDate)
                              ->get();
        
        // Orders in progress
        $inProgressToday = Order::where('picker_id', $pickerId)
                                ->where('status', 'processing')
                                ->whereDate('updated_at', $selectedDate)
                                ->get();
        
        // Backorders created today
        $backordersToday = Backorder::whereHas('originalOrder', function($query) use ($pickerId) {
                                       $query->where('picker_id', $pickerId);
                                   })
                                   ->whereDate('created_at', $selectedDate)
                                   ->with(['originalOrder', 'product'])
                                   ->get();
        
        // Items packed today
        $itemsPackedToday = Cart::whereHas('order', function($query) use ($pickerId) {
                                   $query->where('picker_id', $pickerId);
                               })
                               ->whereNotNull('packed_at')
                               ->whereDate('packed_at', $selectedDate)
                               ->sum('packed_quantity');
        
        // Calculate statistics
        $stats = [
            'total_orders' => $ordersToday->count(),
            'completed_orders' => $completedToday->count(),
            'in_progress_orders' => $inProgressToday->count(),
            'total_backorders' => $backordersToday->count(),
            'items_packed' => $itemsPackedToday,
            'completion_rate' => $ordersToday->count() > 0 ? round(($completedToday->count() / $ordersToday->count()) * 100, 1) : 0,
            'total_value' => $completedToday->sum('total_amount'),
            'backorder_value' => $backordersToday->sum('total_amount')
        ];
        
        // Hourly breakdown
        $hourlyStats = [];
        for ($hour = 8; $hour <= 17; $hour++) {
            $hourStart = $selectedDate->copy()->setHour($hour)->setMinute(0)->setSecond(0);
            $hourEnd = $hourStart->copy()->addHour();
            
            $ordersThisHour = Order::where('picker_id', $pickerId)
                                  ->where('status', 'shipped')
                                  ->whereBetween('updated_at', [$hourStart, $hourEnd])
                                  ->count();
            
            $hourlyStats[] = [
                'hour' => $hour . ':00',
                'orders' => $ordersThisHour
            ];
        }
        
        return view('picker.reports.daily', compact(
            'selectedDate',
            'ordersToday',
            'completedToday', 
            'inProgressToday',
            'backordersToday',
            'stats',
            'hourlyStats'
        ));
    }
}
