@extends('backend.layouts.master')

@section('title','Lamart || Daily Report')

@section('main-content')
<div class="container-fluid">
    @include('backend.layouts.notification')
    
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
      <h1 class="h3 mb-0 text-gray-800">Daily Report - {{$selectedDate->format('M d, Y')}}</h1>
      <div>
        <form method="GET" class="d-inline-block">
          <input type="date" name="date" value="{{$selectedDate->format('Y-m-d')}}" 
                 class="form-control d-inline-block" style="width: auto;" onchange="this.form.submit()">
        </form>
        <a href="{{route('picker')}}" class="btn btn-primary btn-sm ml-2">
          <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
      </div>
    </div>

    <!-- Content Row -->
    <div class="row">
      <!-- Total Orders -->
      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Orders</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$stats['total_orders']}}</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Completed Orders -->
      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Completed</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$stats['completed_orders']}}</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Items Packed -->
      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Items Packed</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$stats['items_packed']}}</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-box fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Completion Rate -->
      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Completion Rate</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$stats['completion_rate']}}%</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-percentage fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Second Row -->
    <div class="row">
      <!-- Order Value -->
      <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Value Shipped</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">${{number_format($stats['total_value'], 2)}}</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Backorders -->
      <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Backorders Created</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$stats['total_backorders']}}</div>
                <div class="text-xs text-muted">${{number_format($stats['backorder_value'], 2)}} value</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- In Progress -->
      <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">In Progress</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$stats['in_progress_orders']}}</div>
              </div>
              <div class="col-auto">
                <i class="fas fa-clock fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Content Row -->
    <div class="row">
      <!-- Hourly Performance Chart -->
      <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
          <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Hourly Performance</h6>
          </div>
          <div class="card-body">
            <div class="chart-area">
              <canvas id="hourlyChart"></canvas>
            </div>
          </div>
        </div>
      </div>

      <!-- Order Details -->
      <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
          <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Order Breakdown</h6>
          </div>
          <div class="card-body">
            <div class="chart-pie pt-4 pb-2">
              <canvas id="orderPieChart"></canvas>
            </div>
            <div class="mt-4 text-center small">
              <span class="mr-2">
                <i class="fas fa-circle text-success"></i> Completed
              </span>
              <span class="mr-2">
                <i class="fas fa-circle text-warning"></i> In Progress
              </span>
              <span class="mr-2">
                <i class="fas fa-circle text-secondary"></i> Pending
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Detailed Tables -->
    <div class="row">
      <!-- Completed Orders -->
      <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
          <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-success">Completed Orders ({{$completedToday->count()}})</h6>
          </div>
          <div class="card-body">
            @if($completedToday->count() > 0)
            <div class="table-responsive">
              <table class="table table-bordered table-sm">
                <thead>
                  <tr>
                    <th>Order #</th>
                    <th>Customer</th>
                    <th>Items</th>
                    <th>Value</th>
                    <th>Completed</th>
                  </tr>
                </thead>
                <tbody>
                  @foreach($completedToday as $order)
                  <tr>
                    <td><strong>{{$order->order_number}}</strong></td>
                    <td>{{$order->first_name}} {{$order->last_name}}</td>
                    <td>{{$order->quantity}}</td>
                    <td>${{number_format($order->total_amount, 2)}}</td>
                    <td>{{$order->updated_at->format('H:i')}}</td>
                  </tr>
                  @endforeach
                </tbody>
              </table>
            </div>
            @else
            <div class="text-center py-3">
              <i class="fas fa-inbox fa-2x text-gray-300 mb-2"></i>
              <p class="text-gray-500">No orders completed today</p>
            </div>
            @endif
          </div>
        </div>
      </div>

      <!-- Backorders Created -->
      <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
          <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-danger">Backorders Created ({{$backordersToday->count()}})</h6>
          </div>
          <div class="card-body">
            @if($backordersToday->count() > 0)
            <div class="table-responsive">
              <table class="table table-bordered table-sm">
                <thead>
                  <tr>
                    <th>Order #</th>
                    <th>Product</th>
                    <th>Qty</th>
                    <th>Value</th>
                    <th>Created</th>
                  </tr>
                </thead>
                <tbody>
                  @foreach($backordersToday as $backorder)
                  <tr>
                    <td><strong>{{$backorder->originalOrder->order_number}}</strong></td>
                    <td>{{$backorder->product->title}}</td>
                    <td>{{$backorder->quantity}}</td>
                    <td>${{number_format($backorder->total_amount, 2)}}</td>
                    <td>{{$backorder->created_at->format('H:i')}}</td>
                  </tr>
                  @endforeach
                </tbody>
              </table>
            </div>
            @else
            <div class="text-center py-3">
              <i class="fas fa-check-circle fa-2x text-gray-300 mb-2"></i>
              <p class="text-gray-500">No backorders created today</p>
            </div>
            @endif
          </div>
        </div>
      </div>
    </div>

</div>
@endsection

@push('scripts')
<!-- Page level plugins -->
<script src="{{asset('backend/vendor/chart.js/Chart.min.js')}}"></script>

<script>
// Hourly Chart
var ctx = document.getElementById("hourlyChart");
var hourlyChart = new Chart(ctx, {
  type: 'line',
  data: {
    labels: [@foreach($hourlyStats as $stat)"{{$stat['hour']}}",@endforeach],
    datasets: [{
      label: "Orders Completed",
      lineTension: 0.3,
      backgroundColor: "rgba(78, 115, 223, 0.05)",
      borderColor: "rgba(78, 115, 223, 1)",
      pointRadius: 3,
      pointBackgroundColor: "rgba(78, 115, 223, 1)",
      pointBorderColor: "rgba(78, 115, 223, 1)",
      pointHoverRadius: 3,
      pointHoverBackgroundColor: "rgba(78, 115, 223, 1)",
      pointHoverBorderColor: "rgba(78, 115, 223, 1)",
      pointHitRadius: 10,
      pointBorderWidth: 2,
      data: [@foreach($hourlyStats as $stat){{$stat['orders']}},@endforeach],
    }],
  },
  options: {
    maintainAspectRatio: false,
    layout: {
      padding: {
        left: 10,
        right: 25,
        top: 25,
        bottom: 0
      }
    },
    scales: {
      xAxes: [{
        time: {
          unit: 'hour'
        },
        gridLines: {
          display: false,
          drawBorder: false
        },
        ticks: {
          maxTicksLimit: 10
        }
      }],
      yAxes: [{
        ticks: {
          maxTicksLimit: 5,
          padding: 10,
          callback: function(value, index, values) {
            return value;
          }
        },
        gridLines: {
          color: "rgb(234, 236, 244)",
          zeroLineColor: "rgb(234, 236, 244)",
          drawBorder: false,
          borderDash: [2],
          zeroLineBorderDash: [2]
        }
      }],
    },
    legend: {
      display: false
    },
    tooltips: {
      backgroundColor: "rgb(255,255,255)",
      bodyFontColor: "#858796",
      titleMarginBottom: 10,
      titleFontColor: '#6e707e',
      titleFontSize: 14,
      borderColor: '#dddfeb',
      borderWidth: 1,
      xPadding: 15,
      yPadding: 15,
      displayColors: false,
      intersect: false,
      mode: 'index',
      caretPadding: 10,
    }
  }
});

// Pie Chart
var ctx2 = document.getElementById("orderPieChart");
var orderPieChart = new Chart(ctx2, {
  type: 'doughnut',
  data: {
    labels: ["Completed", "In Progress", "Pending"],
    datasets: [{
      data: [{{$stats['completed_orders']}}, {{$stats['in_progress_orders']}}, {{$stats['total_orders'] - $stats['completed_orders'] - $stats['in_progress_orders']}}],
      backgroundColor: ['#1cc88a', '#f6c23e', '#6c757d'],
      hoverBackgroundColor: ['#17a673', '#f4b619', '#5a6268'],
      hoverBorderColor: "rgba(234, 236, 244, 1)",
    }],
  },
  options: {
    maintainAspectRatio: false,
    tooltips: {
      backgroundColor: "rgb(255,255,255)",
      bodyFontColor: "#858796",
      borderColor: '#dddfeb',
      borderWidth: 1,
      xPadding: 15,
      yPadding: 15,
      displayColors: false,
      caretPadding: 10,
    },
    legend: {
      display: false
    },
    cutoutPercentage: 80,
  },
});
</script>
@endpush
