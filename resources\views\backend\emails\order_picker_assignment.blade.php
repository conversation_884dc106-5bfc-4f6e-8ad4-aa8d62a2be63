<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Order Assignment</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: linear-gradient(135deg, #4D734E, #5A5863);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 0 0 8px 8px;
            border: 1px solid #ddd;
        }
        .order-details {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #4D734E;
        }
        .customer-info {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .btn {
            display: inline-block;
            background: #4D734E;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
        .btn:hover {
            background: #3a5a3c;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 14px;
        }
        .highlight {
            background: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏭 LAMART</h1>
        <h2>New Order Assignment</h2>
    </div>

    <div class="content">
        <h3>Hello {{$picker->first_name}} {{$picker->last_name}},</h3>
        
        <p>You have been assigned a new order for picking and packing. Please review the details below and begin processing as soon as possible.</p>

        <div class="highlight">
            <strong>⚡ Action Required:</strong> This order has been sent to the warehouse and is ready for picking.
        </div>

        <div class="order-details">
            <h4>📦 Order Information</h4>
            <table>
                <tr>
                    <th>Order Number:</th>
                    <td><strong>{{$order->order_number}}</strong></td>
                </tr>
                <tr>
                    <th>Order Date:</th>
                    <td>{{$order->created_at->format('M d, Y h:i A')}}</td>
                </tr>
                <tr>
                    <th>Total Amount:</th>
                    <td>${{number_format($order->total_amount, 2)}}</td>
                </tr>
                <tr>
                    <th>Total Items:</th>
                    <td>{{$order->cart_info->count()}} items</td>
                </tr>
                <tr>
                    <th>Delivery Method:</th>
                    <td>{{ucfirst($order->delivery_method ?? 'Standard')}}</td>
                </tr>
                <tr>
                    <th>Assigned By:</th>
                    <td>{{$assigned_by->first_name}} {{$assigned_by->last_name}}</td>
                </tr>
            </table>
        </div>

        <div class="customer-info">
            <h4>👤 Customer Information</h4>
            <table>
                <tr>
                    <th>Name:</th>
                    <td>{{$order->first_name}} {{$order->last_name}}</td>
                </tr>
                <tr>
                    <th>Email:</th>
                    <td>{{$order->email}}</td>
                </tr>
                <tr>
                    <th>Phone:</th>
                    <td>{{$order->phone}}</td>
                </tr>
                <tr>
                    <th>Address:</th>
                    <td>
                        {{$order->address1}}<br>
                        @if($order->address2){{$order->address2}}<br>@endif
                        {{$order->country}}, {{$order->post_code}}
                    </td>
                </tr>
            </table>
            
            @if($order->delivery_instructions)
            <div style="margin-top: 15px; padding: 10px; background: #e3f2fd; border-radius: 5px;">
                <strong>📝 Delivery Instructions:</strong><br>
                {{$order->delivery_instructions}}
            </div>
            @endif
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{{url('/picker/orders/show/' . $order->id)}}" class="btn">
                📋 View Order Details
            </a>
            <a href="{{url('/picker/orders')}}" class="btn" style="background: #17a2b8;">
                📦 Go to My Orders
            </a>
        </div>

        <div class="highlight">
            <h4>📋 Next Steps:</h4>
            <ol>
                <li>Log into your picker dashboard</li>
                <li>Review the order details and items</li>
                <li>Start the packing process</li>
                <li>Update item status as you pack</li>
                <li>Generate packing slip when complete</li>
            </ol>
        </div>

        <p><strong>Important:</strong> Please ensure all items are carefully checked and packed according to our quality standards. If you encounter any issues with inventory or have questions about this order, please contact the office immediately.</p>
    </div>

    <div class="footer">
        <p>This is an automated notification from the Lamart Order Management System.</p>
        <p>If you have any questions, please contact your supervisor or the office.</p>
        <p>&copy; {{date('Y')}} Lamart. All rights reserved.</p>
    </div>
</body>
</html>
