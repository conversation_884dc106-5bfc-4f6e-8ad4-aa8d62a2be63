<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Backorder extends Model
{
    use HasFactory;

    protected $fillable = [
        'original_order_id',
        'cart_item_id',
        'product_id',
        'color_id',
        'quantity',
        'price',
        'status',
        'new_order_id',
        'notes',
        'fulfilled_at'
    ];

    protected $casts = [
        'fulfilled_at' => 'datetime'
    ];

    public function originalOrder()
    {
        return $this->belongsTo(Order::class, 'original_order_id');
    }

    public function cartItem()
    {
        return $this->belongsTo(Cart::class, 'cart_item_id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function color()
    {
        return $this->belongsTo(Color::class, 'color_id');
    }

    public function newOrder()
    {
        return $this->belongsTo(Order::class, 'new_order_id');
    }

    public function fulfill($newOrderId = null)
    {
        $this->status = 'fulfilled';
        $this->fulfilled_at = now();
        if ($newOrderId) {
            $this->new_order_id = $newOrderId;
        }
        $this->save();
    }

    public function cancel($reason = null)
    {
        $this->status = 'cancelled';
        if ($reason) {
            $this->notes = $reason;
        }
        $this->save();
    }

    public function combineWithOrder($orderId)
    {
        $this->status = 'combined';
        $this->new_order_id = $orderId;
        $this->fulfilled_at = now();
        $this->save();
    }

    public function getTotalAmountAttribute()
    {
        return $this->quantity * $this->price;
    }

    public static function getCustomerBackorders($customerId)
    {
        return static::whereHas('originalOrder', function ($query) use ($customerId) {
            $query->where('user_id', $customerId);
        })->where('status', 'pending')->get();
    }

    public static function createBackorderOrder($customerId, $backorderIds)
    {
        $backorders = static::whereIn('id', $backorderIds)
                           ->where('status', 'pending')
                           ->get();

        if ($backorders->isEmpty()) {
            return null;
        }

        $customer = User::find($customerId);
        $originalOrder = $backorders->first()->originalOrder;

        // Create new order for backorders
        $newOrder = Order::create([
            'order_number' => 'BO-' . time() . '-' . rand(1000, 9999),
            'user_id' => $customerId,
            'salesman_id' => $originalOrder->salesman_id,
            'sub_total' => $backorders->sum('total_amount'),
            'total_amount' => $backorders->sum('total_amount'),
            'quantity' => $backorders->sum('quantity'),
            'first_name' => $customer->first_name,
            'last_name' => $customer->last_name,
            'email' => $customer->email,
            'phone' => $customer->contact_phone ?? $customer->account_phone,
            'country' => $originalOrder->country,
            'post_code' => $originalOrder->post_code,
            'address1' => $originalOrder->address1,
            'address2' => $originalOrder->address2,
            'delivery_method' => $originalOrder->delivery_method,
            'status' => 'pending'
        ]);

        // Create cart items for the new order
        foreach ($backorders as $backorder) {
            Cart::create([
                'user_id' => $customerId,
                'product_id' => $backorder->product_id,
                'order_id' => $newOrder->id,
                'quantity' => $backorder->quantity,
                'price' => $backorder->price,
                'amount' => $backorder->total_amount,
                'color' => $backorder->color_id,
                'status' => 'new'
            ]);

            // Mark backorder as fulfilled
            $backorder->fulfill($newOrder->id);
        }

        return $newOrder;
    }
}
