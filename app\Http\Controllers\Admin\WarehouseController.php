<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\User;
use App\Models\Backorder;
use App\Models\OrderBox;

class WarehouseController extends Controller
{
    public function dashboard()
    {
        // Order statistics
        $totalOrders = Order::count();
        $pendingOrders = Order::where('status', 'pending')->count();
        $warehouseOrders = Order::where('status', 'sent_to_warehouse')->count();
        $processingOrders = Order::where('status', 'processing')->count();
        $shippedOrders = Order::where('status', 'shipped')->count();

        // Picker statistics
        $totalPickers = User::where('role', 'picker')->where('status', 'active')->count();
        $activePickers = Order::where('status', 'processing')
                             ->whereNotNull('picker_id')
                             ->distinct('picker_id')
                             ->count();

        // Backorder statistics
        $totalBackorders = Backorder::where('status', 'pending')->count();
        $backorderValue = Backorder::where('status', 'pending')->sum('total_amount');

        // Recent activity
        $recentOrders = Order::with(['user', 'picker'])
                            ->whereIn('status', ['sent_to_warehouse', 'processing'])
                            ->latest()
                            ->limit(10)
                            ->get();

        $recentBackorders = Backorder::with(['originalOrder', 'product'])
                                   ->where('status', 'pending')
                                   ->latest()
                                   ->limit(5)
                                   ->get();

        // Picker performance
        $pickerPerformance = User::where('role', 'picker')
                                ->where('status', 'active')
                                ->withCount([
                                    'pickerOrders as total_orders',
                                    'pickerOrders as completed_orders' => function($query) {
                                        $query->where('status', 'shipped');
                                    },
                                    'pickerOrders as processing_orders' => function($query) {
                                        $query->where('status', 'processing');
                                    }
                                ])
                                ->get();

        return view('backend.warehouse.dashboard', compact(
            'totalOrders',
            'pendingOrders', 
            'warehouseOrders',
            'processingOrders',
            'shippedOrders',
            'totalPickers',
            'activePickers',
            'totalBackorders',
            'backorderValue',
            'recentOrders',
            'recentBackorders',
            'pickerPerformance'
        ));
    }
}
